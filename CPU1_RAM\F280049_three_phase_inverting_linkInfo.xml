<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v22.6.1.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x688dba0d</link_time>
   <link_errors>0x0</link_errors>
   <output_file>F280049_three_phase_inverting.out</output_file>
   <entry_point>
      <name>code_start</name>
      <address>0x0</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\</path>
         <kind>object</kind>
         <file>OLED.obj</file>
         <name>OLED.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\</path>
         <kind>object</kind>
         <file>VSG.obj</file>
         <name>VSG.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\syscfg\</path>
         <kind>object</kind>
         <file>board.obj</file>
         <name>board.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\syscfg\</path>
         <kind>object</kind>
         <file>c2000ware_libraries.obj</file>
         <name>c2000ware_libraries.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\</path>
         <kind>object</kind>
         <file>lab_main.obj</file>
         <name>lab_main.obj</name>
      </input_file>
      <input_file id="fl-7">
         <path>.\device\</path>
         <kind>object</kind>
         <file>device.obj</file>
         <name>device.obj</name>
      </input_file>
      <input_file id="fl-8">
         <path>.\device\</path>
         <kind>object</kind>
         <file>f28004x_codestartbranch.obj</file>
         <name>f28004x_codestartbranch.obj</name>
      </input_file>
      <input_file id="fl-9">
         <path>.\headers\source\</path>
         <kind>object</kind>
         <file>f28004x_globalvariabledefs.obj</file>
         <name>f28004x_globalvariabledefs.obj</name>
      </input_file>
      <input_file id="fl-10">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>adc.obj</name>
      </input_file>
      <input_file id="fl-15">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>cmpss.obj</name>
      </input_file>
      <input_file id="fl-16">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>dcc.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>gpio.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>interrupt.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>sysctl.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>O:\Ti\C20005.01\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>xbar.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\ti\controlSUITE\libs\app_libs\solar\v1.2\float\lib\</path>
         <kind>archive</kind>
         <file>Solar_Lib_Float.lib</file>
         <name>PID_GRANDO_F.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\controlSUITE\libs\app_libs\solar\v1.2\float\lib\</path>
         <kind>archive</kind>
         <file>Solar_Lib_Float.lib</file>
         <name>DQ0_ABC_F.obj</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\controlSUITE\libs\app_libs\solar\v1.2\float\lib\</path>
         <kind>archive</kind>
         <file>Solar_Lib_Float.lib</file>
         <name>CNTL_2P2Z_F.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fs_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>i_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-c2000_22.6.1.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-8c">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-150">
         <name>.TI.ramfunc</name>
         <load_address>0xf6</load_address>
         <run_address>0xf6</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.text</name>
         <load_address>0xc000</load_address>
         <run_address>0xc000</run_address>
         <size>0xdd8</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text:retain</name>
         <load_address>0xcdd8</load_address>
         <run_address>0xcdd8</run_address>
         <size>0x4fe</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.text</name>
         <load_address>0xd2d6</load_address>
         <run_address>0xd2d6</run_address>
         <size>0x368</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text</name>
         <load_address>0xd63e</load_address>
         <run_address>0xd63e</run_address>
         <size>0x2fa</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.text</name>
         <load_address>0xd938</load_address>
         <run_address>0xd938</run_address>
         <size>0x20e</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.text:_DCC_verifyClockFrequency</name>
         <load_address>0xdb46</load_address>
         <run_address>0xdb46</run_address>
         <size>0xcb</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.text:_SysCtl_setClock</name>
         <load_address>0xdc11</load_address>
         <run_address>0xdc11</run_address>
         <size>0xc0</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.text:_SysCtl_isPLLValid</name>
         <load_address>0xdcd1</load_address>
         <run_address>0xdcd1</run_address>
         <size>0xbd</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text</name>
         <load_address>0xdd8e</load_address>
         <run_address>0xdd8e</run_address>
         <size>0xb8</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-171">
         <name>.text</name>
         <load_address>0xde46</load_address>
         <run_address>0xde46</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-165">
         <name>.text</name>
         <load_address>0xdece</load_address>
         <run_address>0xdece</run_address>
         <size>0x87</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.text:_DCC_setCounterSeeds</name>
         <load_address>0xdf55</load_address>
         <run_address>0xdf55</run_address>
         <size>0x73</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.text:_GPIO_setControllerCore</name>
         <load_address>0xdfc8</load_address>
         <run_address>0xdfc8</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text</name>
         <load_address>0xdfff</load_address>
         <run_address>0xdfff</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.text:_DCC_setCounterSeeds</name>
         <load_address>0xf1b6</load_address>
         <run_address>0xf1b6</run_address>
         <size>0x73</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.text:_SysCtl_getClock</name>
         <load_address>0xf229</load_address>
         <run_address>0xf229</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text</name>
         <load_address>0xf287</load_address>
         <run_address>0xf287</run_address>
         <size>0x57</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.text</name>
         <load_address>0xf2de</load_address>
         <run_address>0xf2de</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.text:_GPIO_setPadConfig</name>
         <load_address>0xf334</load_address>
         <run_address>0xf334</run_address>
         <size>0x52</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text:_CMPSS_configFilterHigh</name>
         <load_address>0xf386</load_address>
         <run_address>0xf386</run_address>
         <size>0x49</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text:_CMPSS_configFilterLow</name>
         <load_address>0xf3cf</load_address>
         <run_address>0xf3cf</run_address>
         <size>0x49</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.text:_SysCtl_selectOscSource</name>
         <load_address>0xf418</load_address>
         <run_address>0xf418</run_address>
         <size>0x49</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text:_CMPSS_configRamp</name>
         <load_address>0xf461</load_address>
         <run_address>0xf461</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1ec">
         <name>.text:_SysCtl_pollX1Counter</name>
         <load_address>0xf4a9</load_address>
         <run_address>0xf4a9</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.text:_Interrupt_initModule</name>
         <load_address>0xf4ee</load_address>
         <run_address>0xf4ee</run_address>
         <size>0x3d</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.text:_GPIO_setAnalogMode</name>
         <load_address>0xf52b</load_address>
         <run_address>0xf52b</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.text:_GPIO_setPinConfig</name>
         <load_address>0xf565</load_address>
         <run_address>0xf565</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.text:_GPIO_setQualificationMode</name>
         <load_address>0xf59c</load_address>
         <run_address>0xf59c</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.text:_Interrupt_enable</name>
         <load_address>0xf5d3</load_address>
         <run_address>0xf5d3</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.text:_SysCtl_selectXTAL</name>
         <load_address>0xf60a</load_address>
         <run_address>0xf60a</run_address>
         <size>0x36</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.text:_GPIO_setDirectionMode</name>
         <load_address>0xf640</load_address>
         <run_address>0xf640</run_address>
         <size>0x31</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.text:_XBAR_setEPWMMuxConfig</name>
         <load_address>0xf671</load_address>
         <run_address>0xf671</run_address>
         <size>0x2f</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.text:_XBAR_setOutputMuxConfig</name>
         <load_address>0xf6a0</load_address>
         <run_address>0xf6a0</run_address>
         <size>0x2e</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.text:_CMPSS_configLatchOnPWMSYNC</name>
         <load_address>0xf6ce</load_address>
         <run_address>0xf6ce</run_address>
         <size>0x2b</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.text</name>
         <load_address>0xf6f9</load_address>
         <run_address>0xf6f9</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text:_ADC_setOffsetTrimAll</name>
         <load_address>0xf722</load_address>
         <run_address>0xf722</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.text:_CMPSS_isBaseValid</name>
         <load_address>0xf74a</load_address>
         <run_address>0xf74a</run_address>
         <size>0x27</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.text:_DCC_enableSingleShotMode</name>
         <load_address>0xf771</load_address>
         <run_address>0xf771</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e8">
         <name>.text:_DCC_enableSingleShotMode</name>
         <load_address>0xf795</load_address>
         <run_address>0xf795</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text</name>
         <load_address>0xf7b9</load_address>
         <run_address>0xf7b9</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text</name>
         <load_address>0xf7dd</load_address>
         <run_address>0xf7dd</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.text:_SysCtl_selectXTALSingleEnded</name>
         <load_address>0xf7ff</load_address>
         <run_address>0xf7ff</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.text:_DCC_setCounter1ClkSource</name>
         <load_address>0xf81f</load_address>
         <run_address>0xf81f</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.text:_DCC_setCounter1ClkSource</name>
         <load_address>0xf83d</load_address>
         <run_address>0xf83d</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.text:_Interrupt_initVectorTable</name>
         <load_address>0xf85b</load_address>
         <run_address>0xf85b</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.text</name>
         <load_address>0xf879</load_address>
         <run_address>0xf879</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.text:_DCC_getErrorStatus</name>
         <load_address>0xf896</load_address>
         <run_address>0xf896</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.text:_DCC_getSingleShotStatus</name>
         <load_address>0xf8b2</load_address>
         <run_address>0xf8b2</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.text:_DCC_setCounter0ClkSource</name>
         <load_address>0xf8ce</load_address>
         <run_address>0xf8ce</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.text:_DCC_setCounter0ClkSource</name>
         <load_address>0xf8ea</load_address>
         <run_address>0xf8ea</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.text:_SysCtl_getLowSpeedClock</name>
         <load_address>0xf906</load_address>
         <run_address>0xf906</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.text:_SysCtl_enablePeripheral</name>
         <load_address>0xf920</load_address>
         <run_address>0xf920</run_address>
         <size>0x17</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.text:_DCC_disableDoneSignal</name>
         <load_address>0xf937</load_address>
         <run_address>0xf937</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.text:_DCC_disableDoneSignal</name>
         <load_address>0xf94d</load_address>
         <run_address>0xf94d</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.text:_DCC_enableDoneSignal</name>
         <load_address>0xf963</load_address>
         <run_address>0xf963</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1ea">
         <name>.text:_DCC_enableDoneSignal</name>
         <load_address>0xf979</load_address>
         <run_address>0xf979</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text:_Interrupt_defaultHandler</name>
         <load_address>0xf98f</load_address>
         <run_address>0xf98f</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.text:_DCC_clearDoneFlag</name>
         <load_address>0xf9a5</load_address>
         <run_address>0xf9a5</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.text:_DCC_clearDoneFlag</name>
         <load_address>0xf9ba</load_address>
         <run_address>0xf9ba</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.text:_DCC_clearErrorFlag</name>
         <load_address>0xf9cf</load_address>
         <run_address>0xf9cf</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.text:_DCC_clearErrorFlag</name>
         <load_address>0xf9e4</load_address>
         <run_address>0xf9e4</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.text:_DCC_disableErrorSignal</name>
         <load_address>0xf9f9</load_address>
         <run_address>0xf9f9</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.text:_DCC_disableErrorSignal</name>
         <load_address>0xfa0e</load_address>
         <run_address>0xfa0e</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.text:_DCC_enableErrorSignal</name>
         <load_address>0xfa23</load_address>
         <run_address>0xfa23</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e9">
         <name>.text:_DCC_enableErrorSignal</name>
         <load_address>0xfa38</load_address>
         <run_address>0xfa38</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.text:_DCC_disableModule</name>
         <load_address>0xfa4d</load_address>
         <run_address>0xfa4d</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.text:_DCC_disableModule</name>
         <load_address>0xfa61</load_address>
         <run_address>0xfa61</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.text:_DCC_enableModule</name>
         <load_address>0xfa75</load_address>
         <run_address>0xfa75</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1eb">
         <name>.text:_DCC_enableModule</name>
         <load_address>0xfa89</load_address>
         <run_address>0xfa89</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.text:_GPIO_isPinValid</name>
         <load_address>0xfa9d</load_address>
         <run_address>0xfa9d</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.text</name>
         <load_address>0xfab1</load_address>
         <run_address>0xfab1</run_address>
         <size>0x12</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.text:_DCC_isBaseValid</name>
         <load_address>0xfac3</load_address>
         <run_address>0xfac3</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1ee">
         <name>.text:_DCC_isBaseValid</name>
         <load_address>0xfad3</load_address>
         <run_address>0xfad3</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.text:_Interrupt_disableGlobal</name>
         <load_address>0xfae3</load_address>
         <run_address>0xfae3</run_address>
         <size>0xd</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.text:_Interrupt_enableGlobal</name>
         <load_address>0xfaf0</load_address>
         <run_address>0xfaf0</run_address>
         <size>0xd</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.text:_SysCtl_isMCDClockFailureDetected</name>
         <load_address>0xfafd</load_address>
         <run_address>0xfafd</run_address>
         <size>0xb</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text:_Interrupt_illegalOperationHandler</name>
         <load_address>0xfb08</load_address>
         <run_address>0xfb08</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text:_Interrupt_nmiHandler</name>
         <load_address>0xfb12</load_address>
         <run_address>0xfb12</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.text</name>
         <load_address>0xfb1c</load_address>
         <run_address>0xfb1c</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text</name>
         <load_address>0xfb25</load_address>
         <run_address>0xfb25</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1ed">
         <name>.text:_SysCtl_resetMCD</name>
         <load_address>0xfb2d</load_address>
         <run_address>0xfb2d</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text</name>
         <load_address>0xfb34</load_address>
         <run_address>0xfb34</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.cinit:_F8X16</name>
         <load_address>0xe000</load_address>
         <run_address>0xe000</run_address>
         <size>0x5f3</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.cinit:_F6x8</name>
         <load_address>0xe5f3</load_address>
         <run_address>0xe5f3</run_address>
         <size>0x22b</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.cinit</name>
         <load_address>0xe81e</load_address>
         <run_address>0xe81e</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.cinit</name>
         <load_address>0xe82c</load_address>
         <run_address>0xe82c</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.cinit</name>
         <load_address>0xe836</load_address>
         <run_address>0xe836</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.cinit:__lock</name>
         <load_address>0xe840</load_address>
         <run_address>0xe840</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.cinit:__unlock</name>
         <load_address>0xe845</load_address>
         <run_address>0xe845</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-282">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-1c3">
         <name>.ebss:_F6x8</name>
         <uninitialized>true</uninitialized>
         <run_address>0xee80</run_address>
         <size>0x228</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.ebss:_F8X16</name>
         <uninitialized>true</uninitialized>
         <run_address>0xe880</run_address>
         <size>0x5f0</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xf0c0</run_address>
         <size>0xf6</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xe852</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-191">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xe84c</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xe856</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xe858</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-99">
         <name>.econst:.string</name>
         <load_address>0xa800</load_address>
         <run_address>0xa800</run_address>
         <size>0x1b6</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-151">
         <name>.econst:.string</name>
         <load_address>0xa9b6</load_address>
         <run_address>0xa9b6</run_address>
         <size>0x9a</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-141">
         <name>.econst:.string</name>
         <load_address>0xaa50</load_address>
         <run_address>0xaa50</run_address>
         <size>0x98</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.econst:.string</name>
         <load_address>0xaae8</load_address>
         <run_address>0xaae8</run_address>
         <size>0x97</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-af">
         <name>.econst:.string</name>
         <load_address>0xab80</load_address>
         <run_address>0xab80</run_address>
         <size>0x5d</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.econst:.string</name>
         <load_address>0xabde</load_address>
         <run_address>0xabde</run_address>
         <size>0x51</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-135">
         <name>.econst:.string</name>
         <load_address>0xac30</load_address>
         <run_address>0xac30</run_address>
         <size>0x4d</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.econst:.string</name>
         <load_address>0xac7e</load_address>
         <run_address>0xac7e</run_address>
         <size>0x4b</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.econst:.string</name>
         <load_address>0xacca</load_address>
         <run_address>0xacca</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.econst:.string</name>
         <load_address>0xad12</load_address>
         <run_address>0xad12</run_address>
         <size>0x47</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12a">
         <name>PieVectTableFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xd00</run_address>
         <size>0x1c0</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fd">
         <name>AccessProtectionRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f4c0</run_address>
         <size>0x2e</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-112">
         <name>AdcaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7400</run_address>
         <size>0x76</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-113">
         <name>AdcbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7480</run_address>
         <size>0x76</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-114">
         <name>AdccRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7500</run_address>
         <size>0x76</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-df">
         <name>AdcaResultRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb00</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e0">
         <name>AdcbResultRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb20</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e1">
         <name>AdccResultRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xb40</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-117">
         <name>AnalogSubsysRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d700</run_address>
         <size>0x90</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-127">
         <name>CanaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x48000</run_address>
         <size>0x164</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-128">
         <name>CanbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4a000</run_address>
         <size>0x164</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f3">
         <name>ClaPromCrc0RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x61c0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10a">
         <name>Cla1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x1400</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-122">
         <name>Clb1DataExchRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3200</run_address>
         <size>0x108</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-123">
         <name>Clb2DataExchRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3600</run_address>
         <size>0x108</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-124">
         <name>Clb3DataExchRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3a00</run_address>
         <size>0x108</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-125">
         <name>Clb4DataExchRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3e00</run_address>
         <size>0x108</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10b">
         <name>Clb1LogicCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3000</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10c">
         <name>Clb2LogicCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3400</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10d">
         <name>Clb3LogicCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3800</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10e">
         <name>Clb4LogicCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3c00</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-103">
         <name>Clb1LogicCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3100</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-104">
         <name>Clb2LogicCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3500</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-105">
         <name>Clb3LogicCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3900</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-106">
         <name>Clb4LogicCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x3d00</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-107">
         <name>CLBXbarRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7a40</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fe">
         <name>ClkCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d200</run_address>
         <size>0x34</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-eb">
         <name>Cmpss1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5c80</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ec">
         <name>Cmpss2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5ca0</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ed">
         <name>Cmpss3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5cc0</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ee">
         <name>Cmpss4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5ce0</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ef">
         <name>Cmpss5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d00</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f0">
         <name>Cmpss6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d20</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f1">
         <name>Cmpss7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d40</run_address>
         <size>0x1b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-be">
         <name>CpuTimer0RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-bf">
         <name>CpuTimer1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c0">
         <name>CpuTimer2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-116">
         <name>CpuSysRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d300</run_address>
         <size>0x82</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-bb">
         <name>DacaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5c00</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-bc">
         <name>DacbRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5c10</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fc">
         <name>Dcc0RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e700</run_address>
         <size>0x2c</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f7">
         <name>DcsmBank0Z1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f000</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f8">
         <name>DcsmBank0Z2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f040</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f4">
         <name>DcsmBank1Z1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f100</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f5">
         <name>DcsmBank1Z2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f140</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d6">
         <name>DcsmCommonRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f070</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-126">
         <name>DevCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d000</run_address>
         <size>0x132</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e2">
         <name>DmaClaSrcSelRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7980</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-118">
         <name>DmaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e3">
         <name>ECap1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5200</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e4">
         <name>ECap2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5240</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e5">
         <name>ECap3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5280</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e6">
         <name>ECap4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x52c0</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e7">
         <name>ECap5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5300</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e8">
         <name>ECap6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5340</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e9">
         <name>ECap7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5380</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11a">
         <name>EPwm1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4000</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11b">
         <name>EPwm2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4100</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11c">
         <name>EPwm3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4200</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11d">
         <name>EPwm4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4300</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11e">
         <name>EPwm5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4400</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11f">
         <name>EPwm6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4500</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-120">
         <name>EPwm7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4600</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-121">
         <name>EPwm8RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x4700</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-108">
         <name>EPwmXbarRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7a00</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ff">
         <name>EQep1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5100</run_address>
         <size>0x36</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-100">
         <name>EQep2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5140</run_address>
         <size>0x36</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c9">
         <name>EradCounter1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e980</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ca">
         <name>EradCounter2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e990</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cb">
         <name>EradCounter3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e9a0</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cc">
         <name>EradCounter4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e9b0</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d4">
         <name>EradGlobalRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e800</run_address>
         <size>0xb</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c1">
         <name>EradHWBP1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e900</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c2">
         <name>EradHWBP2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e908</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c3">
         <name>EradHWBP3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e910</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c4">
         <name>EradHWBP4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e918</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c5">
         <name>EradHWBP5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e920</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c6">
         <name>EradHWBP6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e928</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c7">
         <name>EradHWBP7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e930</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c8">
         <name>EradHWBP8RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e938</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-129">
         <name>Flash0CtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f800</run_address>
         <size>0x182</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fa">
         <name>Flash0EccRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5fb00</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10f">
         <name>FsiRxaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6680</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-110">
         <name>FsiTxaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6600</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12b">
         <name>GpioCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7c00</run_address>
         <size>0x200</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-101">
         <name>GpioDataRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7f00</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-dd">
         <name>HRCap6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5360</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-de">
         <name>HRCap7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x53a0</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f9">
         <name>I2caRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7300</run_address>
         <size>0x22</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f6">
         <name>InputXbarRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7900</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-119">
         <name>LinaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6a00</run_address>
         <size>0xec</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-102">
         <name>MemoryErrorRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f500</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-115">
         <name>MemCfgRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5f400</run_address>
         <size>0x76</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-bd">
         <name>NmiIntruptRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7060</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-109">
         <name>OutputXbarRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7a80</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12c">
         <name>SysPeriphAcRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5d500</run_address>
         <size>0x200</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cd">
         <name>Pga1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b00</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ce">
         <name>Pga2RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b10</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cf">
         <name>Pga3RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b20</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d0">
         <name>Pga4RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b30</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d1">
         <name>Pga5RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b40</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d2">
         <name>Pga6RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b50</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d3">
         <name>Pga7RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5b60</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ea">
         <name>PieCtrlRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-f2">
         <name>PmbusaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6400</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d7">
         <name>SciaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7200</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d8">
         <name>ScibRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7210</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-111">
         <name>Sdfm1RegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x5e00</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d9">
         <name>SpiaRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6100</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-da">
         <name>SpibRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x6110</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ba">
         <name>SyncSocRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7940</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-db">
         <name>UidRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x703cc</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fb">
         <name>WdRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7000</run_address>
         <size>0x2b</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-dc">
         <name>XbarRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7920</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d5">
         <name>XintRegsFile</name>
         <uninitialized>true</uninitialized>
         <run_address>0x7070</run_address>
         <size>0xb</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a66</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_info</name>
         <load_address>0x1a66</load_address>
         <run_address>0x1a66</run_address>
         <size>0x519</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_info</name>
         <load_address>0x1f7f</load_address>
         <run_address>0x1f7f</run_address>
         <size>0xc086</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0xe005</load_address>
         <run_address>0xe005</run_address>
         <size>0x347</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0xe34c</load_address>
         <run_address>0xe34c</run_address>
         <size>0xa282</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_info</name>
         <load_address>0x185ce</load_address>
         <run_address>0x185ce</run_address>
         <size>0x24e6</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_info</name>
         <load_address>0x1aab4</load_address>
         <run_address>0x1aab4</run_address>
         <size>0x141</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_info</name>
         <load_address>0x1abf5</load_address>
         <run_address>0x1abf5</run_address>
         <size>0x55c22</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_info</name>
         <load_address>0x70817</load_address>
         <run_address>0x70817</run_address>
         <size>0x6a9</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_info</name>
         <load_address>0x70ec0</load_address>
         <run_address>0x70ec0</run_address>
         <size>0xd36</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_info</name>
         <load_address>0x71bf6</load_address>
         <run_address>0x71bf6</run_address>
         <size>0x1cf9</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_info</name>
         <load_address>0x738ef</load_address>
         <run_address>0x738ef</run_address>
         <size>0x1113</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_info</name>
         <load_address>0x74a02</load_address>
         <run_address>0x74a02</run_address>
         <size>0xcb5</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_info</name>
         <load_address>0x756b7</load_address>
         <run_address>0x756b7</run_address>
         <size>0x2e83</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_info</name>
         <load_address>0x7853a</load_address>
         <run_address>0x7853a</run_address>
         <size>0x3093</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_info</name>
         <load_address>0x7b5cd</load_address>
         <run_address>0x7b5cd</run_address>
         <size>0x6bc</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_info</name>
         <load_address>0x7bc89</load_address>
         <run_address>0x7bc89</run_address>
         <size>0x50b</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_info</name>
         <load_address>0x7c194</load_address>
         <run_address>0x7c194</run_address>
         <size>0x6b5</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_info</name>
         <load_address>0x7c849</load_address>
         <run_address>0x7c849</run_address>
         <size>0x173</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_info</name>
         <load_address>0x7c9bc</load_address>
         <run_address>0x7c9bc</run_address>
         <size>0x114</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_info</name>
         <load_address>0x7cad0</load_address>
         <run_address>0x7cad0</run_address>
         <size>0x141</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_info</name>
         <load_address>0x7cc11</load_address>
         <run_address>0x7cc11</run_address>
         <size>0x561</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_info</name>
         <load_address>0x7d172</load_address>
         <run_address>0x7d172</run_address>
         <size>0x43a</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_info</name>
         <load_address>0x7d5ac</load_address>
         <run_address>0x7d5ac</run_address>
         <size>0x3ad</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_info</name>
         <load_address>0x7d959</load_address>
         <run_address>0x7d959</run_address>
         <size>0x3aa</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_info</name>
         <load_address>0x7dd03</load_address>
         <run_address>0x7dd03</run_address>
         <size>0x544</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_info</name>
         <load_address>0x7e247</load_address>
         <run_address>0x7e247</run_address>
         <size>0x4cc</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_info</name>
         <load_address>0x7e713</load_address>
         <run_address>0x7e713</run_address>
         <size>0x467</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-285">
         <name>.debug_info</name>
         <load_address>0x7eb7a</load_address>
         <run_address>0x7eb7a</run_address>
         <size>0xb0</size>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2bc</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_frame</name>
         <load_address>0x2bc</load_address>
         <run_address>0x2bc</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_frame</name>
         <load_address>0x2e4</load_address>
         <run_address>0x2e4</run_address>
         <size>0xa68</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0xd4c</load_address>
         <run_address>0xd4c</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_frame</name>
         <load_address>0xd74</load_address>
         <run_address>0xd74</run_address>
         <size>0x200</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_frame</name>
         <load_address>0xf74</load_address>
         <run_address>0xf74</run_address>
         <size>0x194</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_frame</name>
         <load_address>0x1108</load_address>
         <run_address>0x1108</run_address>
         <size>0x44</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_frame</name>
         <load_address>0x114c</load_address>
         <run_address>0x114c</run_address>
         <size>0xb4</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_frame</name>
         <load_address>0x1200</load_address>
         <run_address>0x1200</run_address>
         <size>0x1f4</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_frame</name>
         <load_address>0x13f4</load_address>
         <run_address>0x13f4</run_address>
         <size>0xec</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_frame</name>
         <load_address>0x14e0</load_address>
         <run_address>0x14e0</run_address>
         <size>0x10c</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-152">
         <name>.debug_frame</name>
         <load_address>0x15ec</load_address>
         <run_address>0x15ec</run_address>
         <size>0x2c0</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_frame</name>
         <load_address>0x18ac</load_address>
         <run_address>0x18ac</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_frame</name>
         <load_address>0x1b3c</load_address>
         <run_address>0x1b3c</run_address>
         <size>0xc6</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_frame</name>
         <load_address>0x1c02</load_address>
         <run_address>0x1c02</run_address>
         <size>0xbe</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-167">
         <name>.debug_frame</name>
         <load_address>0x1cc0</load_address>
         <run_address>0x1cc0</run_address>
         <size>0xd6</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_frame</name>
         <load_address>0x190c</load_address>
         <run_address>0x190c</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_frame</name>
         <load_address>0x196c</load_address>
         <run_address>0x196c</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_frame</name>
         <load_address>0x19ac</load_address>
         <run_address>0x19ac</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.debug_frame</name>
         <load_address>0x19ec</load_address>
         <run_address>0x19ec</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_frame</name>
         <load_address>0x1a2c</load_address>
         <run_address>0x1a2c</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-197">
         <name>.debug_frame</name>
         <load_address>0x1a8c</load_address>
         <run_address>0x1a8c</run_address>
         <size>0x70</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_frame</name>
         <load_address>0x1afc</load_address>
         <run_address>0x1afc</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4c7</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x4c7</load_address>
         <run_address>0x4c7</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_line</name>
         <load_address>0x4f0</load_address>
         <run_address>0x4f0</run_address>
         <size>0xebe</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_line</name>
         <load_address>0x13ae</load_address>
         <run_address>0x13ae</run_address>
         <size>0x3d</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0x13eb</load_address>
         <run_address>0x13eb</run_address>
         <size>0x39f</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_line</name>
         <load_address>0x178a</load_address>
         <run_address>0x178a</run_address>
         <size>0x306</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x1a90</load_address>
         <run_address>0x1a90</run_address>
         <size>0x6a</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_line</name>
         <load_address>0x1afa</load_address>
         <run_address>0x1afa</run_address>
         <size>0xe2</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0x1bdc</load_address>
         <run_address>0x1bdc</run_address>
         <size>0x17a</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_line</name>
         <load_address>0x1d56</load_address>
         <run_address>0x1d56</run_address>
         <size>0x2aa</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_line</name>
         <load_address>0x2000</load_address>
         <run_address>0x2000</run_address>
         <size>0x244</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_line</name>
         <load_address>0x2244</load_address>
         <run_address>0x2244</run_address>
         <size>0x1f0</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_line</name>
         <load_address>0x2434</load_address>
         <run_address>0x2434</run_address>
         <size>0x5e6</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_line</name>
         <load_address>0x2a1a</load_address>
         <run_address>0x2a1a</run_address>
         <size>0xb5</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_line</name>
         <load_address>0x2acf</load_address>
         <run_address>0x2acf</run_address>
         <size>0x74</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_line</name>
         <load_address>0x2b43</load_address>
         <run_address>0x2b43</run_address>
         <size>0x67</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-168">
         <name>.debug_line</name>
         <load_address>0x2baa</load_address>
         <run_address>0x2baa</run_address>
         <size>0x94</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_line</name>
         <load_address>0x2c3e</load_address>
         <run_address>0x2c3e</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_line</name>
         <load_address>0x2cbc</load_address>
         <run_address>0x2cbc</run_address>
         <size>0xad</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_line</name>
         <load_address>0x2d69</load_address>
         <run_address>0x2d69</run_address>
         <size>0x69</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_line</name>
         <load_address>0x2dd2</load_address>
         <run_address>0x2dd2</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_line</name>
         <load_address>0x2e22</load_address>
         <run_address>0x2e22</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-187">
         <name>.debug_line</name>
         <load_address>0x2e84</load_address>
         <run_address>0x2e84</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_line</name>
         <load_address>0x2ec2</load_address>
         <run_address>0x2ec2</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_line</name>
         <load_address>0x2efc</load_address>
         <run_address>0x2efc</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-198">
         <name>.debug_line</name>
         <load_address>0x2f61</load_address>
         <run_address>0x2f61</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_line</name>
         <load_address>0x2fbf</load_address>
         <run_address>0x2fbf</run_address>
         <size>0x4f</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x193</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_abbrev</name>
         <load_address>0x193</load_address>
         <run_address>0x193</run_address>
         <size>0xac</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_abbrev</name>
         <load_address>0x23f</load_address>
         <run_address>0x23f</run_address>
         <size>0x203</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_abbrev</name>
         <load_address>0x442</load_address>
         <run_address>0x442</run_address>
         <size>0x63</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_abbrev</name>
         <load_address>0x4a5</load_address>
         <run_address>0x4a5</run_address>
         <size>0x268</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_abbrev</name>
         <load_address>0x70d</load_address>
         <run_address>0x70d</run_address>
         <size>0x1de</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x8eb</load_address>
         <run_address>0x8eb</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x90c</load_address>
         <run_address>0x90c</run_address>
         <size>0xcb</size>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0x9d7</load_address>
         <run_address>0x9d7</run_address>
         <size>0x14f</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0xb26</load_address>
         <run_address>0xb26</run_address>
         <size>0xef</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_abbrev</name>
         <load_address>0xc15</load_address>
         <run_address>0xc15</run_address>
         <size>0x1cd</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_abbrev</name>
         <load_address>0xde2</load_address>
         <run_address>0xde2</run_address>
         <size>0x1cf</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.debug_abbrev</name>
         <load_address>0xfb1</load_address>
         <run_address>0xfb1</run_address>
         <size>0x18d</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-154">
         <name>.debug_abbrev</name>
         <load_address>0x113e</load_address>
         <run_address>0x113e</run_address>
         <size>0x1f6</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_abbrev</name>
         <load_address>0x1334</load_address>
         <run_address>0x1334</run_address>
         <size>0xf5</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_abbrev</name>
         <load_address>0x1429</load_address>
         <run_address>0x1429</run_address>
         <size>0xc8</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_abbrev</name>
         <load_address>0x14f1</load_address>
         <run_address>0x14f1</run_address>
         <size>0xb1</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_abbrev</name>
         <load_address>0x15a2</load_address>
         <run_address>0x15a2</run_address>
         <size>0xb1</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_abbrev</name>
         <load_address>0x1653</load_address>
         <run_address>0x1653</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_abbrev</name>
         <load_address>0x1699</load_address>
         <run_address>0x1699</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_abbrev</name>
         <load_address>0x16d1</load_address>
         <run_address>0x16d1</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_abbrev</name>
         <load_address>0x1709</load_address>
         <run_address>0x1709</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_abbrev</name>
         <load_address>0x17ec</load_address>
         <run_address>0x17ec</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-188">
         <name>.debug_abbrev</name>
         <load_address>0x1894</load_address>
         <run_address>0x1894</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_abbrev</name>
         <load_address>0x18fc</load_address>
         <run_address>0x18fc</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-194">
         <name>.debug_abbrev</name>
         <load_address>0x1962</load_address>
         <run_address>0x1962</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-199">
         <name>.debug_abbrev</name>
         <load_address>0x1a92</load_address>
         <run_address>0x1a92</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_abbrev</name>
         <load_address>0x1b44</load_address>
         <run_address>0x1b44</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-286">
         <name>.debug_abbrev</name>
         <load_address>0x1c32</load_address>
         <run_address>0x1c32</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd8</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_aranges</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_aranges</name>
         <load_address>0xf0</load_address>
         <run_address>0xf0</run_address>
         <size>0x318</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_aranges</name>
         <load_address>0x408</load_address>
         <run_address>0x408</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_aranges</name>
         <load_address>0x420</load_address>
         <run_address>0x420</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_aranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_aranges</name>
         <load_address>0x510</load_address>
         <run_address>0x510</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_aranges</name>
         <load_address>0x538</load_address>
         <run_address>0x538</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_aranges</name>
         <load_address>0x558</load_address>
         <run_address>0x558</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_aranges</name>
         <load_address>0x598</load_address>
         <run_address>0x598</run_address>
         <size>0x98</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_aranges</name>
         <load_address>0x630</load_address>
         <run_address>0x630</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_aranges</name>
         <load_address>0x680</load_address>
         <run_address>0x680</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-155">
         <name>.debug_aranges</name>
         <load_address>0x6d8</load_address>
         <run_address>0x6d8</run_address>
         <size>0xd8</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_aranges</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_aranges</name>
         <load_address>0x7d8</load_address>
         <run_address>0x7d8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_aranges</name>
         <load_address>0x7f8</load_address>
         <run_address>0x7f8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_aranges</name>
         <load_address>0x818</load_address>
         <run_address>0x818</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_aranges</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_aranges</name>
         <load_address>0x860</load_address>
         <run_address>0x860</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-189">
         <name>.debug_aranges</name>
         <load_address>0x880</load_address>
         <run_address>0x880</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_aranges</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_aranges</name>
         <load_address>0x8c0</load_address>
         <run_address>0x8c0</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_aranges</name>
         <load_address>0x8e8</load_address>
         <run_address>0x8e8</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_aranges</name>
         <load_address>0x918</load_address>
         <run_address>0x918</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <load_address>0xf6</load_address>
         <run_address>0xf6</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-150"/>
         </contents>
      </logical_group>
      <logical_group id="lg-283" display="no" color="cyan">
         <name>.text.1</name>
         <load_address>0xc000</load_address>
         <run_address>0xc000</run_address>
         <size>0x2000</size>
         <contents>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-284" display="no" color="cyan">
         <name>.text.2</name>
         <load_address>0xf1b6</load_address>
         <run_address>0xf1b6</run_address>
         <size>0x980</size>
         <contents>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-1ec"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-1e8"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-1ea"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-1e9"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-1eb"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1ee"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-1ed"/>
            <object_component_ref idref="oc-1a1"/>
         </contents>
      </logical_group>
      <split_section id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <contents>
            <logical_group_ref idref="lg-283"/>
            <logical_group_ref idref="lg-284"/>
         </contents>
      </split_section>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xe000</load_address>
         <run_address>0xe000</run_address>
         <size>0x84c</size>
         <contents>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-1c6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-16c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x400</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-282"/>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0xe84c</run_address>
         <size>0x96a</size>
         <contents>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1dc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0xa800</load_address>
         <run_address>0xa800</run_address>
         <size>0x559</size>
         <contents>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-a7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.cio</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>ramgs0</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>ramgs1</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>PieVectTableFile</name>
         <run_address>0xd00</run_address>
         <size>0x1c0</size>
         <contents>
            <object_component_ref idref="oc-12a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>EmuKeyVar</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14" display="no" color="cyan">
         <name>EmuBModeVar</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15" display="no" color="cyan">
         <name>EmuBootPinsVar</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16" display="no" color="cyan">
         <name>FlashCallbackVar</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17" display="no" color="cyan">
         <name>FlashScalingVar</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>GROUP_2</name>
         <run_address>0xd00</run_address>
         <size>0x0</size>
         <contents>
            <logical_group_ref idref="lg-13"/>
            <logical_group_ref idref="lg-14"/>
            <logical_group_ref idref="lg-15"/>
            <logical_group_ref idref="lg-16"/>
            <logical_group_ref idref="lg-17"/>
         </contents>
      </logical_group>
      <overlay id="lg-10" display="no" color="cyan">
         <name>UNION_1</name>
         <run_address>0xd00</run_address>
         <size>0x1c0</size>
         <contents>
            <logical_group_ref idref="lg-11"/>
            <logical_group_ref idref="lg-12"/>
         </contents>
      </overlay>
      <logical_group id="lg-18" display="no" color="cyan">
         <name>AccessProtectionRegsFile</name>
         <run_address>0x5f4c0</run_address>
         <size>0x2e</size>
         <contents>
            <object_component_ref idref="oc-fd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-19" display="no" color="cyan">
         <name>AdcaRegsFile</name>
         <run_address>0x7400</run_address>
         <size>0x76</size>
         <contents>
            <object_component_ref idref="oc-112"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a" display="no" color="cyan">
         <name>AdcbRegsFile</name>
         <run_address>0x7480</run_address>
         <size>0x76</size>
         <contents>
            <object_component_ref idref="oc-113"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b" display="no" color="cyan">
         <name>AdccRegsFile</name>
         <run_address>0x7500</run_address>
         <size>0x76</size>
         <contents>
            <object_component_ref idref="oc-114"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c" display="no" color="cyan">
         <name>AdcaResultRegsFile</name>
         <run_address>0xb00</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-df"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1d" display="no" color="cyan">
         <name>AdcbResultRegsFile</name>
         <run_address>0xb20</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-e0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1e" display="no" color="cyan">
         <name>AdccResultRegsFile</name>
         <run_address>0xb40</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-e1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f" display="no" color="cyan">
         <name>AnalogSubsysRegsFile</name>
         <run_address>0x5d700</run_address>
         <size>0x90</size>
         <contents>
            <object_component_ref idref="oc-117"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20" display="no" color="cyan">
         <name>CanaRegsFile</name>
         <run_address>0x48000</run_address>
         <size>0x164</size>
         <contents>
            <object_component_ref idref="oc-127"/>
         </contents>
      </logical_group>
      <logical_group id="lg-21" display="no" color="cyan">
         <name>CanbRegsFile</name>
         <run_address>0x4a000</run_address>
         <size>0x164</size>
         <contents>
            <object_component_ref idref="oc-128"/>
         </contents>
      </logical_group>
      <logical_group id="lg-22" display="no" color="cyan">
         <name>ClaPromCrc0RegsFile</name>
         <run_address>0x61c0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-f3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-23" display="no" color="cyan">
         <name>Cla1RegsFile</name>
         <run_address>0x1400</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-10a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24" display="no" color="cyan">
         <name>Clb1DataExchRegsFile</name>
         <run_address>0x3200</run_address>
         <size>0x108</size>
         <contents>
            <object_component_ref idref="oc-122"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25" display="no" color="cyan">
         <name>Clb2DataExchRegsFile</name>
         <run_address>0x3600</run_address>
         <size>0x108</size>
         <contents>
            <object_component_ref idref="oc-123"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26" display="no" color="cyan">
         <name>Clb3DataExchRegsFile</name>
         <run_address>0x3a00</run_address>
         <size>0x108</size>
         <contents>
            <object_component_ref idref="oc-124"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27" display="no" color="cyan">
         <name>Clb4DataExchRegsFile</name>
         <run_address>0x3e00</run_address>
         <size>0x108</size>
         <contents>
            <object_component_ref idref="oc-125"/>
         </contents>
      </logical_group>
      <logical_group id="lg-28" display="no" color="cyan">
         <name>Clb1LogicCfgRegsFile</name>
         <run_address>0x3000</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-10b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-29" display="no" color="cyan">
         <name>Clb2LogicCfgRegsFile</name>
         <run_address>0x3400</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-10c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2a" display="no" color="cyan">
         <name>Clb3LogicCfgRegsFile</name>
         <run_address>0x3800</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-10d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2b" display="no" color="cyan">
         <name>Clb4LogicCfgRegsFile</name>
         <run_address>0x3c00</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-10e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2c" display="no" color="cyan">
         <name>Clb1LogicCtrlRegsFile</name>
         <run_address>0x3100</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-103"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2d" display="no" color="cyan">
         <name>Clb2LogicCtrlRegsFile</name>
         <run_address>0x3500</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-104"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2e" display="no" color="cyan">
         <name>Clb3LogicCtrlRegsFile</name>
         <run_address>0x3900</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-105"/>
         </contents>
      </logical_group>
      <logical_group id="lg-2f" display="no" color="cyan">
         <name>Clb4LogicCtrlRegsFile</name>
         <run_address>0x3d00</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-106"/>
         </contents>
      </logical_group>
      <logical_group id="lg-30" display="no" color="cyan">
         <name>CLBXbarRegsFile</name>
         <run_address>0x7a40</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-107"/>
         </contents>
      </logical_group>
      <logical_group id="lg-31" display="no" color="cyan">
         <name>ClkCfgRegsFile</name>
         <run_address>0x5d200</run_address>
         <size>0x34</size>
         <contents>
            <object_component_ref idref="oc-fe"/>
         </contents>
      </logical_group>
      <logical_group id="lg-32" display="no" color="cyan">
         <name>Cmpss1RegsFile</name>
         <run_address>0x5c80</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-eb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-33" display="no" color="cyan">
         <name>Cmpss2RegsFile</name>
         <run_address>0x5ca0</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-ec"/>
         </contents>
      </logical_group>
      <logical_group id="lg-34" display="no" color="cyan">
         <name>Cmpss3RegsFile</name>
         <run_address>0x5cc0</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-ed"/>
         </contents>
      </logical_group>
      <logical_group id="lg-35" display="no" color="cyan">
         <name>Cmpss4RegsFile</name>
         <run_address>0x5ce0</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-ee"/>
         </contents>
      </logical_group>
      <logical_group id="lg-36" display="no" color="cyan">
         <name>Cmpss5RegsFile</name>
         <run_address>0x5d00</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-ef"/>
         </contents>
      </logical_group>
      <logical_group id="lg-37" display="no" color="cyan">
         <name>Cmpss6RegsFile</name>
         <run_address>0x5d20</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-f0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-38" display="no" color="cyan">
         <name>Cmpss7RegsFile</name>
         <run_address>0x5d40</run_address>
         <size>0x1b</size>
         <contents>
            <object_component_ref idref="oc-f1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-39" display="no" color="cyan">
         <name>CpuTimer0RegsFile</name>
         <run_address>0xc00</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-be"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3a" display="no" color="cyan">
         <name>CpuTimer1RegsFile</name>
         <run_address>0xc08</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-bf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3b" display="no" color="cyan">
         <name>CpuTimer2RegsFile</name>
         <run_address>0xc10</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3c" display="no" color="cyan">
         <name>CpuSysRegsFile</name>
         <run_address>0x5d300</run_address>
         <size>0x82</size>
         <contents>
            <object_component_ref idref="oc-116"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3d" display="no" color="cyan">
         <name>DacaRegsFile</name>
         <run_address>0x5c00</run_address>
         <size>0x7</size>
         <contents>
            <object_component_ref idref="oc-bb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3e" display="no" color="cyan">
         <name>DacbRegsFile</name>
         <run_address>0x5c10</run_address>
         <size>0x7</size>
         <contents>
            <object_component_ref idref="oc-bc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3f" display="no" color="cyan">
         <name>Dcc0RegsFile</name>
         <run_address>0x5e700</run_address>
         <size>0x2c</size>
         <contents>
            <object_component_ref idref="oc-fc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-40" display="no" color="cyan">
         <name>DcsmBank0Z1RegsFile</name>
         <run_address>0x5f000</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-f7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-41" display="no" color="cyan">
         <name>DcsmBank0Z2RegsFile</name>
         <run_address>0x5f040</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-f8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-42" display="no" color="cyan">
         <name>DcsmBank1Z1RegsFile</name>
         <run_address>0x5f100</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-f4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-43" display="no" color="cyan">
         <name>DcsmBank1Z2RegsFile</name>
         <run_address>0x5f140</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-f5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-44" display="no" color="cyan">
         <name>DcsmCommonRegsFile</name>
         <run_address>0x5f070</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-d6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-45" display="no" color="cyan">
         <name>DevCfgRegsFile</name>
         <run_address>0x5d000</run_address>
         <size>0x132</size>
         <contents>
            <object_component_ref idref="oc-126"/>
         </contents>
      </logical_group>
      <logical_group id="lg-46" display="no" color="cyan">
         <name>DmaClaSrcSelRegsFile</name>
         <run_address>0x7980</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-47" display="no" color="cyan">
         <name>DmaRegsFile</name>
         <run_address>0x1000</run_address>
         <size>0xe0</size>
         <contents>
            <object_component_ref idref="oc-118"/>
         </contents>
      </logical_group>
      <logical_group id="lg-48" display="no" color="cyan">
         <name>ECap1RegsFile</name>
         <run_address>0x5200</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-49" display="no" color="cyan">
         <name>ECap2RegsFile</name>
         <run_address>0x5240</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4a" display="no" color="cyan">
         <name>ECap3RegsFile</name>
         <run_address>0x5280</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4b" display="no" color="cyan">
         <name>ECap4RegsFile</name>
         <run_address>0x52c0</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4c" display="no" color="cyan">
         <name>ECap5RegsFile</name>
         <run_address>0x5300</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4d" display="no" color="cyan">
         <name>ECap6RegsFile</name>
         <run_address>0x5340</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4e" display="no" color="cyan">
         <name>ECap7RegsFile</name>
         <run_address>0x5380</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-e9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4f" display="no" color="cyan">
         <name>EPwm1RegsFile</name>
         <run_address>0x4000</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-50" display="no" color="cyan">
         <name>EPwm2RegsFile</name>
         <run_address>0x4100</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-51" display="no" color="cyan">
         <name>EPwm3RegsFile</name>
         <run_address>0x4200</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-52" display="no" color="cyan">
         <name>EPwm4RegsFile</name>
         <run_address>0x4300</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-53" display="no" color="cyan">
         <name>EPwm5RegsFile</name>
         <run_address>0x4400</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-54" display="no" color="cyan">
         <name>EPwm6RegsFile</name>
         <run_address>0x4500</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-11f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-55" display="no" color="cyan">
         <name>EPwm7RegsFile</name>
         <run_address>0x4600</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-120"/>
         </contents>
      </logical_group>
      <logical_group id="lg-56" display="no" color="cyan">
         <name>EPwm8RegsFile</name>
         <run_address>0x4700</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-121"/>
         </contents>
      </logical_group>
      <logical_group id="lg-57" display="no" color="cyan">
         <name>EPwmXbarRegsFile</name>
         <run_address>0x7a00</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-108"/>
         </contents>
      </logical_group>
      <logical_group id="lg-58" display="no" color="cyan">
         <name>EQep1RegsFile</name>
         <run_address>0x5100</run_address>
         <size>0x36</size>
         <contents>
            <object_component_ref idref="oc-ff"/>
         </contents>
      </logical_group>
      <logical_group id="lg-59" display="no" color="cyan">
         <name>EQep2RegsFile</name>
         <run_address>0x5140</run_address>
         <size>0x36</size>
         <contents>
            <object_component_ref idref="oc-100"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5a" display="no" color="cyan">
         <name>EradCounter1RegsFile</name>
         <run_address>0x5e980</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-c9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5b" display="no" color="cyan">
         <name>EradCounter2RegsFile</name>
         <run_address>0x5e990</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-ca"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5c" display="no" color="cyan">
         <name>EradCounter3RegsFile</name>
         <run_address>0x5e9a0</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5d" display="no" color="cyan">
         <name>EradCounter4RegsFile</name>
         <run_address>0x5e9b0</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-cc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5e" display="no" color="cyan">
         <name>EradGlobalRegsFile</name>
         <run_address>0x5e800</run_address>
         <size>0xb</size>
         <contents>
            <object_component_ref idref="oc-d4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5f" display="no" color="cyan">
         <name>EradHWBP1RegsFile</name>
         <run_address>0x5e900</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-60" display="no" color="cyan">
         <name>EradHWBP2RegsFile</name>
         <run_address>0x5e908</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-61" display="no" color="cyan">
         <name>EradHWBP3RegsFile</name>
         <run_address>0x5e910</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-62" display="no" color="cyan">
         <name>EradHWBP4RegsFile</name>
         <run_address>0x5e918</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-63" display="no" color="cyan">
         <name>EradHWBP5RegsFile</name>
         <run_address>0x5e920</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-64" display="no" color="cyan">
         <name>EradHWBP6RegsFile</name>
         <run_address>0x5e928</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-65" display="no" color="cyan">
         <name>EradHWBP7RegsFile</name>
         <run_address>0x5e930</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-66" display="no" color="cyan">
         <name>EradHWBP8RegsFile</name>
         <run_address>0x5e938</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-c8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-67" display="no" color="cyan">
         <name>Flash0CtrlRegsFile</name>
         <run_address>0x5f800</run_address>
         <size>0x182</size>
         <contents>
            <object_component_ref idref="oc-129"/>
         </contents>
      </logical_group>
      <logical_group id="lg-68" display="no" color="cyan">
         <name>Flash0EccRegsFile</name>
         <run_address>0x5fb00</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-fa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-69" display="no" color="cyan">
         <name>FsiRxaRegsFile</name>
         <run_address>0x6680</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-10f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6a" display="no" color="cyan">
         <name>FsiTxaRegsFile</name>
         <run_address>0x6600</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-110"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6b" display="no" color="cyan">
         <name>GpioCtrlRegsFile</name>
         <run_address>0x7c00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-12b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6c" display="no" color="cyan">
         <name>GpioDataRegsFile</name>
         <run_address>0x7f00</run_address>
         <size>0x3a</size>
         <contents>
            <object_component_ref idref="oc-101"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6d" display="no" color="cyan">
         <name>HRCap6RegsFile</name>
         <run_address>0x5360</run_address>
         <size>0x16</size>
         <contents>
            <object_component_ref idref="oc-dd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6e" display="no" color="cyan">
         <name>HRCap7RegsFile</name>
         <run_address>0x53a0</run_address>
         <size>0x16</size>
         <contents>
            <object_component_ref idref="oc-de"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6f" display="no" color="cyan">
         <name>I2caRegsFile</name>
         <run_address>0x7300</run_address>
         <size>0x22</size>
         <contents>
            <object_component_ref idref="oc-f9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-70" display="no" color="cyan">
         <name>InputXbarRegsFile</name>
         <run_address>0x7900</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-f6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-71" display="no" color="cyan">
         <name>LinaRegsFile</name>
         <run_address>0x6a00</run_address>
         <size>0xec</size>
         <contents>
            <object_component_ref idref="oc-119"/>
         </contents>
      </logical_group>
      <logical_group id="lg-72" display="no" color="cyan">
         <name>MemoryErrorRegsFile</name>
         <run_address>0x5f500</run_address>
         <size>0x3a</size>
         <contents>
            <object_component_ref idref="oc-102"/>
         </contents>
      </logical_group>
      <logical_group id="lg-73" display="no" color="cyan">
         <name>MemCfgRegsFile</name>
         <run_address>0x5f400</run_address>
         <size>0x76</size>
         <contents>
            <object_component_ref idref="oc-115"/>
         </contents>
      </logical_group>
      <logical_group id="lg-74" display="no" color="cyan">
         <name>NmiIntruptRegsFile</name>
         <run_address>0x7060</run_address>
         <size>0x7</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-75" display="no" color="cyan">
         <name>OutputXbarRegsFile</name>
         <run_address>0x7a80</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-76" display="no" color="cyan">
         <name>SysPeriphAcRegsFile</name>
         <run_address>0x5d500</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-12c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-77" display="no" color="cyan">
         <name>Pga1RegsFile</name>
         <run_address>0x5b00</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-78" display="no" color="cyan">
         <name>Pga2RegsFile</name>
         <run_address>0x5b10</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-79" display="no" color="cyan">
         <name>Pga3RegsFile</name>
         <run_address>0x5b20</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7a" display="no" color="cyan">
         <name>Pga4RegsFile</name>
         <run_address>0x5b30</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7b" display="no" color="cyan">
         <name>Pga5RegsFile</name>
         <run_address>0x5b40</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7c" display="no" color="cyan">
         <name>Pga6RegsFile</name>
         <run_address>0x5b50</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-d2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7d" display="no" color="cyan">
         <name>Pga7RegsFile</name>
         <run_address>0x5b60</run_address>
         <size>0xa</size>
         <contents>
            <object_component_ref idref="oc-d3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7e" display="no" color="cyan">
         <name>PieCtrlRegsFile</name>
         <run_address>0xce0</run_address>
         <size>0x1a</size>
         <contents>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7f" display="no" color="cyan">
         <name>PieVectTableFile</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-80" display="no" color="cyan">
         <name>PmbusaRegsFile</name>
         <run_address>0x6400</run_address>
         <size>0x1e</size>
         <contents>
            <object_component_ref idref="oc-f2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-81" display="no" color="cyan">
         <name>SciaRegsFile</name>
         <run_address>0x7200</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-d7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-82" display="no" color="cyan">
         <name>ScibRegsFile</name>
         <run_address>0x7210</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-d8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-83" display="no" color="cyan">
         <name>Sdfm1RegsFile</name>
         <run_address>0x5e00</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-111"/>
         </contents>
      </logical_group>
      <logical_group id="lg-84" display="no" color="cyan">
         <name>SpiaRegsFile</name>
         <run_address>0x6100</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-d9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-85" display="no" color="cyan">
         <name>SpibRegsFile</name>
         <run_address>0x6110</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-da"/>
         </contents>
      </logical_group>
      <logical_group id="lg-86" display="no" color="cyan">
         <name>SyncSocRegsFile</name>
         <run_address>0x7940</run_address>
         <size>0x6</size>
         <contents>
            <object_component_ref idref="oc-ba"/>
         </contents>
      </logical_group>
      <logical_group id="lg-87" display="no" color="cyan">
         <name>UidRegsFile</name>
         <run_address>0x703cc</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-db"/>
         </contents>
      </logical_group>
      <logical_group id="lg-88" display="no" color="cyan">
         <name>WdRegsFile</name>
         <run_address>0x7000</run_address>
         <size>0x2b</size>
         <contents>
            <object_component_ref idref="oc-fb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-89" display="no" color="cyan">
         <name>XbarRegsFile</name>
         <run_address>0x7920</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-dc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8a" display="no" color="cyan">
         <name>XintRegsFile</name>
         <run_address>0x7070</run_address>
         <size>0xb</size>
         <contents>
            <object_component_ref idref="oc-d5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-276" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7ec2a</size>
         <contents>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-285"/>
         </contents>
      </logical_group>
      <logical_group id="lg-278" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d96</size>
         <contents>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-19c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27a" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x300e</size>
         <contents>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-19d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27c" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c41</size>
         <contents>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-286"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27e" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x938</size>
         <contents>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-19f"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="no" color="green">
         <name>BEGIN</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x2</length>
         <used_space>0x2</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM0</name>
         <page_id>0x0</page_id>
         <origin>0xf6</origin>
         <length>0x30a</length>
         <used_space>0x4</used_space>
         <unused_space>0x306</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xf6</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <available_space>
               <start_address>0xfa</start_address>
               <size>0x306</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS0</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS1</name>
         <page_id>0x0</page_id>
         <origin>0x8800</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS2</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS3</name>
         <page_id>0x0</page_id>
         <origin>0x9800</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS4</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC0</name>
         <page_id>0x0</page_id>
         <origin>0x80000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC1</name>
         <page_id>0x0</page_id>
         <origin>0x81000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC2</name>
         <page_id>0x0</page_id>
         <origin>0x82000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC3</name>
         <page_id>0x0</page_id>
         <origin>0x83000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC4</name>
         <page_id>0x0</page_id>
         <origin>0x84000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC5</name>
         <page_id>0x0</page_id>
         <origin>0x85000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC6</name>
         <page_id>0x0</page_id>
         <origin>0x86000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC7</name>
         <page_id>0x0</page_id>
         <origin>0x87000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC8</name>
         <page_id>0x0</page_id>
         <origin>0x88000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC9</name>
         <page_id>0x0</page_id>
         <origin>0x89000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC10</name>
         <page_id>0x0</page_id>
         <origin>0x8a000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC11</name>
         <page_id>0x0</page_id>
         <origin>0x8b000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC12</name>
         <page_id>0x0</page_id>
         <origin>0x8c000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC13</name>
         <page_id>0x0</page_id>
         <origin>0x8d000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC14</name>
         <page_id>0x0</page_id>
         <origin>0x8e000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC15</name>
         <page_id>0x0</page_id>
         <origin>0x8f000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC0</name>
         <page_id>0x0</page_id>
         <origin>0x90000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC1</name>
         <page_id>0x0</page_id>
         <origin>0x91000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC2</name>
         <page_id>0x0</page_id>
         <origin>0x92000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC3</name>
         <page_id>0x0</page_id>
         <origin>0x93000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC4</name>
         <page_id>0x0</page_id>
         <origin>0x94000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC5</name>
         <page_id>0x0</page_id>
         <origin>0x95000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC6</name>
         <page_id>0x0</page_id>
         <origin>0x96000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC7</name>
         <page_id>0x0</page_id>
         <origin>0x97000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC8</name>
         <page_id>0x0</page_id>
         <origin>0x98000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC9</name>
         <page_id>0x0</page_id>
         <origin>0x99000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC10</name>
         <page_id>0x0</page_id>
         <origin>0x9a000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC11</name>
         <page_id>0x0</page_id>
         <origin>0x9b000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC12</name>
         <page_id>0x0</page_id>
         <origin>0x9c000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC13</name>
         <page_id>0x0</page_id>
         <origin>0x9d000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC14</name>
         <page_id>0x0</page_id>
         <origin>0x9e000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC15</name>
         <page_id>0x0</page_id>
         <origin>0x9f000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BOOT_RSVD</name>
         <page_id>0x1</page_id>
         <origin>0x2</origin>
         <length>0xf1</length>
         <used_space>0x0</used_space>
         <unused_space>0xf1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM1</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x3f8</length>
         <used_space>0x100</used_space>
         <unused_space>0x2f8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x400</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-8"/>
            </allocated_space>
            <available_space>
               <start_address>0x500</start_address>
               <size>0x2f8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADCARESULT</name>
         <page_id>0x1</page_id>
         <origin>0xb00</origin>
         <length>0x18</length>
         <used_space>0x18</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb00</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-1c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADCBRESULT</name>
         <page_id>0x1</page_id>
         <origin>0xb20</origin>
         <length>0x18</length>
         <used_space>0x18</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb20</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-1d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADCCRESULT</name>
         <page_id>0x1</page_id>
         <origin>0xb40</origin>
         <length>0x18</length>
         <used_space>0x18</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xb40</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-1e"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPUTIMER0</name>
         <page_id>0x1</page_id>
         <origin>0xc00</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc00</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-39"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPUTIMER1</name>
         <page_id>0x1</page_id>
         <origin>0xc08</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc08</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-3a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CPUTIMER2</name>
         <page_id>0x1</page_id>
         <origin>0xc10</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc10</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-3b"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIECTRL</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x1a</length>
         <used_space>0x1a</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xce0</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-7e"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>PIEVECTTABLE</name>
         <page_id>0x1</page_id>
         <origin>0xd00</origin>
         <length>0x200</length>
         <used_space>0x1c0</used_space>
         <unused_space>0x40</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xd00</start_address>
               <size>0x1c0</size>
               <logical_group_ref idref="lg-10"/>
            </allocated_space>
            <available_space>
               <start_address>0xec0</start_address>
               <size>0x40</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DMA</name>
         <page_id>0x1</page_id>
         <origin>0x1000</origin>
         <length>0x200</length>
         <used_space>0xe0</used_space>
         <unused_space>0x120</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x1000</start_address>
               <size>0xe0</size>
               <logical_group_ref idref="lg-47"/>
            </allocated_space>
            <available_space>
               <start_address>0x10e0</start_address>
               <size>0x120</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CLA1</name>
         <page_id>0x1</page_id>
         <origin>0x1400</origin>
         <length>0x80</length>
         <used_space>0x48</used_space>
         <unused_space>0x38</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x1400</start_address>
               <size>0x48</size>
               <logical_group_ref idref="lg-23"/>
            </allocated_space>
            <available_space>
               <start_address>0x1448</start_address>
               <size>0x38</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB1LOGICCFG</name>
         <page_id>0x1</page_id>
         <origin>0x3000</origin>
         <length>0x52</length>
         <used_space>0x50</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3000</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-28"/>
            </allocated_space>
            <available_space>
               <start_address>0x3050</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB1LOGICCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x3100</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3100</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CLB1DATAEXCH</name>
         <page_id>0x1</page_id>
         <origin>0x3200</origin>
         <length>0x200</length>
         <used_space>0x108</used_space>
         <unused_space>0xf8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3200</start_address>
               <size>0x108</size>
               <logical_group_ref idref="lg-24"/>
            </allocated_space>
            <available_space>
               <start_address>0x3308</start_address>
               <size>0xf8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB2LOGICCFG</name>
         <page_id>0x1</page_id>
         <origin>0x3400</origin>
         <length>0x52</length>
         <used_space>0x50</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3400</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-29"/>
            </allocated_space>
            <available_space>
               <start_address>0x3450</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB2LOGICCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x3500</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3500</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2d"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CLB2DATAEXCH</name>
         <page_id>0x1</page_id>
         <origin>0x3600</origin>
         <length>0x200</length>
         <used_space>0x108</used_space>
         <unused_space>0xf8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3600</start_address>
               <size>0x108</size>
               <logical_group_ref idref="lg-25"/>
            </allocated_space>
            <available_space>
               <start_address>0x3708</start_address>
               <size>0xf8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB3LOGICCFG</name>
         <page_id>0x1</page_id>
         <origin>0x3800</origin>
         <length>0x52</length>
         <used_space>0x50</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3800</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-2a"/>
            </allocated_space>
            <available_space>
               <start_address>0x3850</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB3LOGICCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x3900</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3900</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2e"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CLB3DATAEXCH</name>
         <page_id>0x1</page_id>
         <origin>0x3a00</origin>
         <length>0x200</length>
         <used_space>0x108</used_space>
         <unused_space>0xf8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3a00</start_address>
               <size>0x108</size>
               <logical_group_ref idref="lg-26"/>
            </allocated_space>
            <available_space>
               <start_address>0x3b08</start_address>
               <size>0xf8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB4LOGICCFG</name>
         <page_id>0x1</page_id>
         <origin>0x3c00</origin>
         <length>0x52</length>
         <used_space>0x50</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3c00</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-2b"/>
            </allocated_space>
            <available_space>
               <start_address>0x3c50</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLB4LOGICCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x3d00</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3d00</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-2f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CLB4DATAEXCH</name>
         <page_id>0x1</page_id>
         <origin>0x3e00</origin>
         <length>0x200</length>
         <used_space>0x108</used_space>
         <unused_space>0xf8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x3e00</start_address>
               <size>0x108</size>
               <logical_group_ref idref="lg-27"/>
            </allocated_space>
            <available_space>
               <start_address>0x3f08</start_address>
               <size>0xf8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM1</name>
         <page_id>0x1</page_id>
         <origin>0x4000</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4000</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-4f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM2</name>
         <page_id>0x1</page_id>
         <origin>0x4100</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4100</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-50"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM3</name>
         <page_id>0x1</page_id>
         <origin>0x4200</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4200</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-51"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM4</name>
         <page_id>0x1</page_id>
         <origin>0x4300</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4300</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-52"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM5</name>
         <page_id>0x1</page_id>
         <origin>0x4400</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4400</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-53"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM6</name>
         <page_id>0x1</page_id>
         <origin>0x4500</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4500</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-54"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM7</name>
         <page_id>0x1</page_id>
         <origin>0x4600</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4600</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-55"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>EPWM8</name>
         <page_id>0x1</page_id>
         <origin>0x4700</origin>
         <length>0x100</length>
         <used_space>0x100</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4700</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-56"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP1</name>
         <page_id>0x1</page_id>
         <origin>0x5100</origin>
         <length>0x40</length>
         <used_space>0x36</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5100</start_address>
               <size>0x36</size>
               <logical_group_ref idref="lg-58"/>
            </allocated_space>
            <available_space>
               <start_address>0x5136</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EQEP2</name>
         <page_id>0x1</page_id>
         <origin>0x5140</origin>
         <length>0x40</length>
         <used_space>0x36</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5140</start_address>
               <size>0x36</size>
               <logical_group_ref idref="lg-59"/>
            </allocated_space>
            <available_space>
               <start_address>0x5176</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP1</name>
         <page_id>0x1</page_id>
         <origin>0x5200</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5200</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-48"/>
            </allocated_space>
            <available_space>
               <start_address>0x521a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP2</name>
         <page_id>0x1</page_id>
         <origin>0x5240</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5240</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-49"/>
            </allocated_space>
            <available_space>
               <start_address>0x525a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP3</name>
         <page_id>0x1</page_id>
         <origin>0x5280</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5280</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-4a"/>
            </allocated_space>
            <available_space>
               <start_address>0x529a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP4</name>
         <page_id>0x1</page_id>
         <origin>0x52c0</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x52c0</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-4b"/>
            </allocated_space>
            <available_space>
               <start_address>0x52da</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP5</name>
         <page_id>0x1</page_id>
         <origin>0x5300</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5300</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-4c"/>
            </allocated_space>
            <available_space>
               <start_address>0x531a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP6</name>
         <page_id>0x1</page_id>
         <origin>0x5340</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5340</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-4d"/>
            </allocated_space>
            <available_space>
               <start_address>0x535a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>HRCAP6</name>
         <page_id>0x1</page_id>
         <origin>0x5360</origin>
         <length>0x20</length>
         <used_space>0x16</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5360</start_address>
               <size>0x16</size>
               <logical_group_ref idref="lg-6d"/>
            </allocated_space>
            <available_space>
               <start_address>0x5376</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ECAP7</name>
         <page_id>0x1</page_id>
         <origin>0x5380</origin>
         <length>0x1e</length>
         <used_space>0x1a</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5380</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-4e"/>
            </allocated_space>
            <available_space>
               <start_address>0x539a</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>HRCAP7</name>
         <page_id>0x1</page_id>
         <origin>0x53a0</origin>
         <length>0x20</length>
         <used_space>0x16</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x53a0</start_address>
               <size>0x16</size>
               <logical_group_ref idref="lg-6e"/>
            </allocated_space>
            <available_space>
               <start_address>0x53b6</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA1</name>
         <page_id>0x1</page_id>
         <origin>0x5b00</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b00</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-77"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b0a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA2</name>
         <page_id>0x1</page_id>
         <origin>0x5b10</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b10</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-78"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b1a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA3</name>
         <page_id>0x1</page_id>
         <origin>0x5b20</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b20</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-79"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b2a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA4</name>
         <page_id>0x1</page_id>
         <origin>0x5b30</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b30</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-7a"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b3a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA5</name>
         <page_id>0x1</page_id>
         <origin>0x5b40</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b40</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-7b"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b4a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA6</name>
         <page_id>0x1</page_id>
         <origin>0x5b50</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b50</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-7c"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b5a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PGA7</name>
         <page_id>0x1</page_id>
         <origin>0x5b60</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5b60</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-7d"/>
            </allocated_space>
            <available_space>
               <start_address>0x5b6a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DACA</name>
         <page_id>0x1</page_id>
         <origin>0x5c00</origin>
         <length>0x8</length>
         <used_space>0x7</used_space>
         <unused_space>0x1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5c00</start_address>
               <size>0x7</size>
               <logical_group_ref idref="lg-3d"/>
            </allocated_space>
            <available_space>
               <start_address>0x5c07</start_address>
               <size>0x1</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DACB</name>
         <page_id>0x1</page_id>
         <origin>0x5c10</origin>
         <length>0x8</length>
         <used_space>0x7</used_space>
         <unused_space>0x1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5c10</start_address>
               <size>0x7</size>
               <logical_group_ref idref="lg-3e"/>
            </allocated_space>
            <available_space>
               <start_address>0x5c17</start_address>
               <size>0x1</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS1</name>
         <page_id>0x1</page_id>
         <origin>0x5c80</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5c80</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-32"/>
            </allocated_space>
            <available_space>
               <start_address>0x5c9b</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS2</name>
         <page_id>0x1</page_id>
         <origin>0x5ca0</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5ca0</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-33"/>
            </allocated_space>
            <available_space>
               <start_address>0x5cbb</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS3</name>
         <page_id>0x1</page_id>
         <origin>0x5cc0</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5cc0</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-34"/>
            </allocated_space>
            <available_space>
               <start_address>0x5cdb</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS4</name>
         <page_id>0x1</page_id>
         <origin>0x5ce0</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5ce0</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-35"/>
            </allocated_space>
            <available_space>
               <start_address>0x5cfb</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS5</name>
         <page_id>0x1</page_id>
         <origin>0x5d00</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d00</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-36"/>
            </allocated_space>
            <available_space>
               <start_address>0x5d1b</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS6</name>
         <page_id>0x1</page_id>
         <origin>0x5d20</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d20</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-37"/>
            </allocated_space>
            <available_space>
               <start_address>0x5d3b</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CMPSS7</name>
         <page_id>0x1</page_id>
         <origin>0x5d40</origin>
         <length>0x20</length>
         <used_space>0x1b</used_space>
         <unused_space>0x5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d40</start_address>
               <size>0x1b</size>
               <logical_group_ref idref="lg-38"/>
            </allocated_space>
            <available_space>
               <start_address>0x5d5b</start_address>
               <size>0x5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SDFM1</name>
         <page_id>0x1</page_id>
         <origin>0x5e00</origin>
         <length>0x70</length>
         <used_space>0x50</used_space>
         <unused_space>0x20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e00</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-83"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e50</start_address>
               <size>0x20</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SPIA</name>
         <page_id>0x1</page_id>
         <origin>0x6100</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6100</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-84"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SPIB</name>
         <page_id>0x1</page_id>
         <origin>0x6110</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6110</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-85"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLAPROMCRC0</name>
         <page_id>0x1</page_id>
         <origin>0x61c0</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x61c0</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-22"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PMBUSA</name>
         <page_id>0x1</page_id>
         <origin>0x6400</origin>
         <length>0x20</length>
         <used_space>0x1e</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6400</start_address>
               <size>0x1e</size>
               <logical_group_ref idref="lg-80"/>
            </allocated_space>
            <available_space>
               <start_address>0x641e</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>FSITXA</name>
         <page_id>0x1</page_id>
         <origin>0x6600</origin>
         <length>0x50</length>
         <used_space>0x50</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6600</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-6a"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>FSIRXA</name>
         <page_id>0x1</page_id>
         <origin>0x6680</origin>
         <length>0x50</length>
         <used_space>0x50</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6680</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-69"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>LINA</name>
         <page_id>0x1</page_id>
         <origin>0x6a00</origin>
         <length>0xec</length>
         <used_space>0xec</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x6a00</start_address>
               <size>0xec</size>
               <logical_group_ref idref="lg-71"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>WD</name>
         <page_id>0x1</page_id>
         <origin>0x7000</origin>
         <length>0x2c</length>
         <used_space>0x2b</used_space>
         <unused_space>0x1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7000</start_address>
               <size>0x2b</size>
               <logical_group_ref idref="lg-88"/>
            </allocated_space>
            <available_space>
               <start_address>0x702b</start_address>
               <size>0x1</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>NMIINTRUPT</name>
         <page_id>0x1</page_id>
         <origin>0x7060</origin>
         <length>0x8</length>
         <used_space>0x7</used_space>
         <unused_space>0x1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7060</start_address>
               <size>0x7</size>
               <logical_group_ref idref="lg-74"/>
            </allocated_space>
            <available_space>
               <start_address>0x7067</start_address>
               <size>0x1</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XINT</name>
         <page_id>0x1</page_id>
         <origin>0x7070</origin>
         <length>0xc</length>
         <used_space>0xb</used_space>
         <unused_space>0x1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7070</start_address>
               <size>0xb</size>
               <logical_group_ref idref="lg-8a"/>
            </allocated_space>
            <available_space>
               <start_address>0x707b</start_address>
               <size>0x1</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIA</name>
         <page_id>0x1</page_id>
         <origin>0x7200</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7200</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-81"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SCIB</name>
         <page_id>0x1</page_id>
         <origin>0x7210</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7210</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-82"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>I2CA</name>
         <page_id>0x1</page_id>
         <origin>0x7300</origin>
         <length>0x22</length>
         <used_space>0x22</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7300</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-6f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ADCA</name>
         <page_id>0x1</page_id>
         <origin>0x7400</origin>
         <length>0x80</length>
         <used_space>0x76</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7400</start_address>
               <size>0x76</size>
               <logical_group_ref idref="lg-19"/>
            </allocated_space>
            <available_space>
               <start_address>0x7476</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ADCB</name>
         <page_id>0x1</page_id>
         <origin>0x7480</origin>
         <length>0x80</length>
         <used_space>0x76</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7480</start_address>
               <size>0x76</size>
               <logical_group_ref idref="lg-1a"/>
            </allocated_space>
            <available_space>
               <start_address>0x74f6</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ADCC</name>
         <page_id>0x1</page_id>
         <origin>0x7500</origin>
         <length>0x80</length>
         <used_space>0x76</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7500</start_address>
               <size>0x76</size>
               <logical_group_ref idref="lg-1b"/>
            </allocated_space>
            <available_space>
               <start_address>0x7576</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>INPUTXBAR</name>
         <page_id>0x1</page_id>
         <origin>0x7900</origin>
         <length>0x20</length>
         <used_space>0x20</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7900</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-70"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>XBAR</name>
         <page_id>0x1</page_id>
         <origin>0x7920</origin>
         <length>0x20</length>
         <used_space>0x10</used_space>
         <unused_space>0x10</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7920</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-89"/>
            </allocated_space>
            <available_space>
               <start_address>0x7930</start_address>
               <size>0x10</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>SYNCSOC</name>
         <page_id>0x1</page_id>
         <origin>0x7940</origin>
         <length>0x6</length>
         <used_space>0x6</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7940</start_address>
               <size>0x6</size>
               <logical_group_ref idref="lg-86"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DMACLASRCSEL</name>
         <page_id>0x1</page_id>
         <origin>0x7980</origin>
         <length>0x1a</length>
         <used_space>0x1a</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7980</start_address>
               <size>0x1a</size>
               <logical_group_ref idref="lg-46"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>EPWMXBAR</name>
         <page_id>0x1</page_id>
         <origin>0x7a00</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7a00</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-57"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLBXBAR</name>
         <page_id>0x1</page_id>
         <origin>0x7a40</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7a40</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-30"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>OUTPUTXBAR</name>
         <page_id>0x1</page_id>
         <origin>0x7a80</origin>
         <length>0x40</length>
         <used_space>0x40</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7a80</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-75"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>GPIOCTRL</name>
         <page_id>0x1</page_id>
         <origin>0x7c00</origin>
         <length>0x200</length>
         <used_space>0x200</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7c00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-6b"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>GPIODATA</name>
         <page_id>0x1</page_id>
         <origin>0x7f00</origin>
         <length>0x40</length>
         <used_space>0x3a</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x7f00</start_address>
               <size>0x3a</size>
               <logical_group_ref idref="lg-6c"/>
            </allocated_space>
            <available_space>
               <start_address>0x7f3a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS5</name>
         <page_id>0x1</page_id>
         <origin>0xa800</origin>
         <length>0x800</length>
         <used_space>0x559</used_space>
         <unused_space>0x2a7</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xa800</start_address>
               <size>0x559</size>
               <logical_group_ref idref="lg-b"/>
            </allocated_space>
            <available_space>
               <start_address>0xad59</start_address>
               <size>0x2a7</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS6</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS7</name>
         <page_id>0x1</page_id>
         <origin>0xb800</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS0</name>
         <page_id>0x1</page_id>
         <origin>0xc000</origin>
         <length>0x2000</length>
         <used_space>0x2000</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xc000</start_address>
               <size>0x2000</size>
               <logical_group_ref idref="lg-283"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS1</name>
         <page_id>0x1</page_id>
         <origin>0xe000</origin>
         <length>0x2000</length>
         <used_space>0x1b36</used_space>
         <unused_space>0x4ca</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xe000</start_address>
               <size>0x84c</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xe84c</start_address>
               <size>0x96a</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xf1b6</start_address>
               <size>0x980</size>
               <logical_group_ref idref="lg-284"/>
            </allocated_space>
            <available_space>
               <start_address>0xfb36</start_address>
               <size>0x4ca</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS2</name>
         <page_id>0x1</page_id>
         <origin>0x10000</origin>
         <length>0x2000</length>
         <used_space>0x0</used_space>
         <unused_space>0x2000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS3</name>
         <page_id>0x1</page_id>
         <origin>0x12000</origin>
         <length>0x1ff8</length>
         <used_space>0x0</used_space>
         <unused_space>0x1ff8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CANA</name>
         <page_id>0x1</page_id>
         <origin>0x48000</origin>
         <length>0x200</length>
         <used_space>0x164</used_space>
         <unused_space>0x9c</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x48000</start_address>
               <size>0x164</size>
               <logical_group_ref idref="lg-20"/>
            </allocated_space>
            <available_space>
               <start_address>0x48164</start_address>
               <size>0x9c</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CANB</name>
         <page_id>0x1</page_id>
         <origin>0x4a000</origin>
         <length>0x200</length>
         <used_space>0x164</used_space>
         <unused_space>0x9c</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4a000</start_address>
               <size>0x164</size>
               <logical_group_ref idref="lg-21"/>
            </allocated_space>
            <available_space>
               <start_address>0x4a164</start_address>
               <size>0x9c</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DEVCFG</name>
         <page_id>0x1</page_id>
         <origin>0x5d000</origin>
         <length>0x180</length>
         <used_space>0x132</used_space>
         <unused_space>0x4e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d000</start_address>
               <size>0x132</size>
               <logical_group_ref idref="lg-45"/>
            </allocated_space>
            <available_space>
               <start_address>0x5d132</start_address>
               <size>0x4e</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CLKCFG</name>
         <page_id>0x1</page_id>
         <origin>0x5d200</origin>
         <length>0x36</length>
         <used_space>0x34</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d200</start_address>
               <size>0x34</size>
               <logical_group_ref idref="lg-31"/>
            </allocated_space>
            <available_space>
               <start_address>0x5d234</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CPUSYS</name>
         <page_id>0x1</page_id>
         <origin>0x5d300</origin>
         <length>0x82</length>
         <used_space>0x82</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d300</start_address>
               <size>0x82</size>
               <logical_group_ref idref="lg-3c"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SYSPERIPHAC</name>
         <page_id>0x1</page_id>
         <origin>0x5d500</origin>
         <length>0x200</length>
         <used_space>0x200</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d500</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-76"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ANALOGSUBSYS</name>
         <page_id>0x1</page_id>
         <origin>0x5d700</origin>
         <length>0x90</length>
         <used_space>0x90</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5d700</start_address>
               <size>0x90</size>
               <logical_group_ref idref="lg-1f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCC0</name>
         <page_id>0x1</page_id>
         <origin>0x5e700</origin>
         <length>0x2c</length>
         <used_space>0x2c</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e700</start_address>
               <size>0x2c</size>
               <logical_group_ref idref="lg-3f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADGLOBAL</name>
         <page_id>0x1</page_id>
         <origin>0x5e800</origin>
         <length>0x14</length>
         <used_space>0xb</used_space>
         <unused_space>0x9</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e800</start_address>
               <size>0xb</size>
               <logical_group_ref idref="lg-5e"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e80b</start_address>
               <size>0x9</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP1</name>
         <page_id>0x1</page_id>
         <origin>0x5e900</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e900</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-5f"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP2</name>
         <page_id>0x1</page_id>
         <origin>0x5e908</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e908</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-60"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP3</name>
         <page_id>0x1</page_id>
         <origin>0x5e910</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e910</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-61"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP4</name>
         <page_id>0x1</page_id>
         <origin>0x5e918</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e918</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-62"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP5</name>
         <page_id>0x1</page_id>
         <origin>0x5e920</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e920</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-63"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP6</name>
         <page_id>0x1</page_id>
         <origin>0x5e928</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e928</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-64"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP7</name>
         <page_id>0x1</page_id>
         <origin>0x5e930</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e930</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-65"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADHWBP8</name>
         <page_id>0x1</page_id>
         <origin>0x5e938</origin>
         <length>0x8</length>
         <used_space>0x8</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e938</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-66"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADCOUNTER1</name>
         <page_id>0x1</page_id>
         <origin>0x5e980</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e980</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-5a"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e98a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADCOUNTER2</name>
         <page_id>0x1</page_id>
         <origin>0x5e990</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e990</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-5b"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e99a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADCOUNTER3</name>
         <page_id>0x1</page_id>
         <origin>0x5e9a0</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e9a0</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-5c"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e9aa</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ERADCOUNTER4</name>
         <page_id>0x1</page_id>
         <origin>0x5e9b0</origin>
         <length>0x10</length>
         <used_space>0xa</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5e9b0</start_address>
               <size>0xa</size>
               <logical_group_ref idref="lg-5d"/>
            </allocated_space>
            <available_space>
               <start_address>0x5e9ba</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCSMBANK0Z1</name>
         <page_id>0x1</page_id>
         <origin>0x5f000</origin>
         <length>0x24</length>
         <used_space>0x22</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f000</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-40"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f022</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCSMBANK0Z2</name>
         <page_id>0x1</page_id>
         <origin>0x5f040</origin>
         <length>0x24</length>
         <used_space>0x22</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f040</start_address>
               <size>0x22</size>
               <logical_group_ref idref="lg-41"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f062</start_address>
               <size>0x2</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCSMCOMMON</name>
         <page_id>0x1</page_id>
         <origin>0x5f070</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f070</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-44"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCSMBANK1Z1</name>
         <page_id>0x1</page_id>
         <origin>0x5f100</origin>
         <length>0x24</length>
         <used_space>0x20</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f100</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-42"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f120</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>DCSMBANK1Z2</name>
         <page_id>0x1</page_id>
         <origin>0x5f140</origin>
         <length>0x24</length>
         <used_space>0x20</used_space>
         <unused_space>0x4</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f140</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-43"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f160</start_address>
               <size>0x4</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>MEMCFG</name>
         <page_id>0x1</page_id>
         <origin>0x5f400</origin>
         <length>0x80</length>
         <used_space>0x76</used_space>
         <unused_space>0xa</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f400</start_address>
               <size>0x76</size>
               <logical_group_ref idref="lg-73"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f476</start_address>
               <size>0xa</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ACCESSPROTECTION</name>
         <page_id>0x1</page_id>
         <origin>0x5f4c0</origin>
         <length>0x40</length>
         <used_space>0x2e</used_space>
         <unused_space>0x12</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f4c0</start_address>
               <size>0x2e</size>
               <logical_group_ref idref="lg-18"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f4ee</start_address>
               <size>0x12</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>MEMORYERROR</name>
         <page_id>0x1</page_id>
         <origin>0x5f500</origin>
         <length>0x40</length>
         <used_space>0x3a</used_space>
         <unused_space>0x6</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f500</start_address>
               <size>0x3a</size>
               <logical_group_ref idref="lg-72"/>
            </allocated_space>
            <available_space>
               <start_address>0x5f53a</start_address>
               <size>0x6</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH0CTRL</name>
         <page_id>0x1</page_id>
         <origin>0x5f800</origin>
         <length>0x182</length>
         <used_space>0x182</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5f800</start_address>
               <size>0x182</size>
               <logical_group_ref idref="lg-67"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>FLASH0ECC</name>
         <page_id>0x1</page_id>
         <origin>0x5fb00</origin>
         <length>0x28</length>
         <used_space>0x28</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5fb00</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-68"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>UID</name>
         <page_id>0x1</page_id>
         <origin>0x703cc</origin>
         <length>0x10</length>
         <used_space>0x10</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x703cc</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-87"/>
            </allocated_space>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0xe000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0xe000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x100</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x500</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-d">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-17e">
         <name>_OLED_Set_Pos</name>
         <value>0xd420</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-180">
         <name>_OLED_Display_Off</name>
         <value>0xd443</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-182">
         <name>_Write_IIC_Data</name>
         <value>0xd3ab</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-184">
         <name>_OLED_ShowString</name>
         <value>0xd583</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-186">
         <name>_OLED_ShowChar</name>
         <value>0xd498</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-188">
         <name>_IIC_Wait_Ack</name>
         <value>0xd347</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-18a">
         <name>_OLED_On</name>
         <value>0xd474</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-18c">
         <name>_OLED_WR_Byte</name>
         <value>0xd3c2</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-18e">
         <name>_Delay_1ms</name>
         <value>0xd405</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-190">
         <name>_OLED_Display_On</name>
         <value>0xd436</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-192">
         <name>_oled_pow</name>
         <value>0xd4f4</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-194">
         <name>_OLED_DrawBMP</name>
         <value>0xd59f</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-196">
         <name>_OLED_Clear</name>
         <value>0xd450</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-198">
         <name>_OLED_Init</name>
         <value>0xd5cd</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-19a">
         <name>_Write_IIC_Byte</name>
         <value>0xd35d</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-19c">
         <name>_IIC_Start</name>
         <value>0xd311</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-19e">
         <name>_IIC_Stop</name>
         <value>0xd32c</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-1a0">
         <name>_Write_IIC_Command</name>
         <value>0xd394</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-1a2">
         <name>_Delay_3us</name>
         <value>0xd415</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>_Delay_50ms</name>
         <value>0xd3f5</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-1a6">
         <name>_fill_picture</name>
         <value>0xd3d0</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-1a8">
         <name>_OLED_ShowNum</name>
         <value>0xd506</value>
         <object_component_ref idref="oc-1aa"/>
      </symbol>
      <symbol id="sm-272">
         <name>_INPUTXBAR_init</name>
         <value>0xcd85</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-274">
         <name>_GPIO32I2CA_SDA_init</name>
         <value>0xcd0d</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-276">
         <name>_GPIO33I2CA_SCL_init</name>
         <value>0xccf9</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-278">
         <name>_INTERRUPT_init</name>
         <value>0xcd8d</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-27a">
         <name>_myOUTPUTXBAR0_init</name>
         <value>0xcda9</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-27c">
         <name>_CMPSS_init</name>
         <value>0xc9a0</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-27e">
         <name>_myBoardLED0_GPIO_init</name>
         <value>0xcd71</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-280">
         <name>_ASYSCTL_init</name>
         <value>0xc99a</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-282">
         <name>_myCMPSS0_init</name>
         <value>0xc9a3</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-284">
         <name>_AKEY4_init</name>
         <value>0xcd5d</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-286">
         <name>_AKEY1_init</name>
         <value>0xcd21</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-288">
         <name>_OUTPUTXBAR_init</name>
         <value>0xcda6</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-28a">
         <name>_AKEY3_init</name>
         <value>0xcd49</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-28c">
         <name>_AKEY2_init</name>
         <value>0xcd35</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-28e">
         <name>_Board_init</name>
         <value>0xc7a5</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-290">
         <name>_PinMux_init</name>
         <value>0xc7c0</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-292">
         <name>_myINPUTXBARINPUT0_init</name>
         <value>0xcd88</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-294">
         <name>_GPIO_init</name>
         <value>0xccea</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-296">
         <name>_EPWMXBAR_init</name>
         <value>0xccde</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-298">
         <name>_myDACB_init</name>
         <value>0xca33</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-29a">
         <name>_myDACA_init</name>
         <value>0xca16</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-29c">
         <name>_DAC_init</name>
         <value>0xca11</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-29e">
         <name>_ADC_init</name>
         <value>0xc83a</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2a0">
         <name>_SYNC_init</name>
         <value>0xcdba</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2a2">
         <name>_myADCB_init</name>
         <value>0xc8b8</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2a4">
         <name>_myADCC_init</name>
         <value>0xc919</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2a6">
         <name>_myADCA_init</name>
         <value>0xc841</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2a8">
         <name>_EPWM_init</name>
         <value>0xca50</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>_myEPWMXBAR0_init</name>
         <value>0xcce1</value>
         <object_component_ref idref="oc-1a9"/>
      </symbol>
      <symbol id="sm-2ee">
         <name>_INT_myADCA_1_ISR</name>
         <value>0xcdd8</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-2f0">
         <name>_main</name>
         <value>0xd69e</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-2f2">
         <name>_dq0_abc1</name>
         <value>0xf140</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>_SetDuty</name>
         <value>0xd8fc</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-2f5">
         <name>_max_min_average</name>
         <value>0xd8c1</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-2f7">
         <name>_Count1</name>
         <value>0xf0c2</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2f8">
         <name>_Variable1</name>
         <value>0xf180</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>_cntl_2p2z_vars_PRA</name>
         <value>0xf0e2</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2fa">
         <name>_cntl_2p2z_vars_PRB</name>
         <value>0xf110</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2fb">
         <name>_cntl_2p2z_vars_PRC</name>
         <value>0xf130</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2fc">
         <name>_cntl_2p2z_coeffs_PRA</name>
         <value>0xf0d2</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2fd">
         <name>_cntl_2p2z_coeffs_PRC</name>
         <value>0xf120</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2fe">
         <name>_cntl_2p2z_coeffs_PRB</name>
         <value>0xf100</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2ff">
         <name>_Probe1</name>
         <value>0xf0c4</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-300">
         <name>_pid_grando_controller_Vdc</name>
         <value>0xf154</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-301">
         <name>_INT_myEPWM1_TZ_ISR</name>
         <value>0xd2c9</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-303">
         <name>_Count</name>
         <value>0xf0c0</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-329">
         <name>_Example_Fail</name>
         <value>0xe854</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-32a">
         <name>_Device_verifyXTAL</name>
         <value>0xdb0c</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-32c">
         <name>_Device_initGPIO</name>
         <value>0xdafc</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-32e">
         <name>___error__</name>
         <value>0xdb3f</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-330">
         <name>_Device_init</name>
         <value>0xd9cd</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-332">
         <name>_Example_PassCount</name>
         <value>0xe852</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-333">
         <name>_Device_enableAllPeripherals</name>
         <value>0xda1c</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-344">
         <name>code_start</name>
         <value>0x0</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-430">
         <name>_WdRegs</name>
         <value>0x7000</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-431">
         <name>_CanaRegs</name>
         <value>0x48000</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-432">
         <name>_Pga3Regs</name>
         <value>0x5b20</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-433">
         <name>_SysPeriphAcRegs</name>
         <value>0x5d500</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-434">
         <name>_GpioCtrlRegs</name>
         <value>0x7c00</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-435">
         <name>_PieVectTable</name>
         <value>0xd00</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-436">
         <name>_Cmpss6Regs</name>
         <value>0x5d20</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-437">
         <name>_ClkCfgRegs</name>
         <value>0x5d200</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-438">
         <name>_Dcc0Regs</name>
         <value>0x5e700</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-439">
         <name>_XbarRegs</name>
         <value>0x7920</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-43a">
         <name>_Clb3DataExchRegs</name>
         <value>0x3a00</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-43b">
         <name>_ECap4Regs</name>
         <value>0x52c0</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-43c">
         <name>_Pga2Regs</name>
         <value>0x5b10</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-43d">
         <name>_Flash0EccRegs</name>
         <value>0x5fb00</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-43e">
         <name>_EradHWBP7Regs</name>
         <value>0x5e930</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-43f">
         <name>_Clb2LogicCfgRegs</name>
         <value>0x3400</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-440">
         <name>_Clb2LogicCtrlRegs</name>
         <value>0x3500</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-441">
         <name>_InputXbarRegs</name>
         <value>0x7900</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-442">
         <name>_EPwmXbarRegs</name>
         <value>0x7a00</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-443">
         <name>_Cmpss7Regs</name>
         <value>0x5d40</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-444">
         <name>_EPwm4Regs</name>
         <value>0x4300</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-445">
         <name>_ECap5Regs</name>
         <value>0x5300</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-446">
         <name>_EradHWBP8Regs</name>
         <value>0x5e938</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-447">
         <name>_EradHWBP6Regs</name>
         <value>0x5e928</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-448">
         <name>_CpuTimer1Regs</name>
         <value>0xc08</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-449">
         <name>_Clb4LogicCtrlRegs</name>
         <value>0x3d00</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-44a">
         <name>_Cmpss4Regs</name>
         <value>0x5ce0</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-44b">
         <name>_Clb1DataExchRegs</name>
         <value>0x3200</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-44c">
         <name>_EPwm5Regs</name>
         <value>0x4400</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-44d">
         <name>_SpiaRegs</name>
         <value>0x6100</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-44e">
         <name>_CpuSysRegs</name>
         <value>0x5d300</value>
         <object_component_ref idref="oc-116"/>
      </symbol>
      <symbol id="sm-44f">
         <name>_CanbRegs</name>
         <value>0x4a000</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-450">
         <name>_ECap6Regs</name>
         <value>0x5340</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-451">
         <name>_DmaRegs</name>
         <value>0x1000</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-452">
         <name>_EradHWBP5Regs</name>
         <value>0x5e920</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-453">
         <name>_PmbusaRegs</name>
         <value>0x6400</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-454">
         <name>_XintRegs</name>
         <value>0x7070</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-455">
         <name>_CpuTimer0Regs</name>
         <value>0xc00</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-456">
         <name>_Cmpss5Regs</name>
         <value>0x5d00</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-457">
         <name>_AccessProtectionRegs</name>
         <value>0x5f4c0</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-458">
         <name>_MemCfgRegs</name>
         <value>0x5f400</value>
         <object_component_ref idref="oc-115"/>
      </symbol>
      <symbol id="sm-459">
         <name>_HRCap7Regs</name>
         <value>0x53a0</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-45a">
         <name>_DacaRegs</name>
         <value>0x5c00</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-45b">
         <name>_EPwm6Regs</name>
         <value>0x4500</value>
         <object_component_ref idref="oc-11f"/>
      </symbol>
      <symbol id="sm-45c">
         <name>_EradCounter4Regs</name>
         <value>0x5e9b0</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-45d">
         <name>_Pga5Regs</name>
         <value>0x5b40</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-45e">
         <name>_AdcaRegs</name>
         <value>0x7400</value>
         <object_component_ref idref="oc-112"/>
      </symbol>
      <symbol id="sm-45f">
         <name>_SciaRegs</name>
         <value>0x7200</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-460">
         <name>_ECap7Regs</name>
         <value>0x5380</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-461">
         <name>_EradHWBP4Regs</name>
         <value>0x5e918</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-462">
         <name>_GpioDataRegs</name>
         <value>0x7f00</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-463">
         <name>_Sdfm1Regs</name>
         <value>0x5e00</value>
         <object_component_ref idref="oc-111"/>
      </symbol>
      <symbol id="sm-464">
         <name>_Cmpss2Regs</name>
         <value>0x5ca0</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-465">
         <name>_DcsmBank0Z2Regs</name>
         <value>0x5f040</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-466">
         <name>_HRCap6Regs</name>
         <value>0x5360</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-467">
         <name>_FsiTxaRegs</name>
         <value>0x6600</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-468">
         <name>_Flash0CtrlRegs</name>
         <value>0x5f800</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-469">
         <name>_EPwm7Regs</name>
         <value>0x4600</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-46a">
         <name>_Clb4DataExchRegs</name>
         <value>0x3e00</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-46b">
         <name>_Pga4Regs</name>
         <value>0x5b30</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-46c">
         <name>_CLBXbarRegs</name>
         <value>0x7a40</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-46d">
         <name>_CpuTimer2Regs</name>
         <value>0xc10</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-46e">
         <name>_Clb3LogicCfgRegs</name>
         <value>0x3800</value>
         <object_component_ref idref="oc-10d"/>
      </symbol>
      <symbol id="sm-46f">
         <name>_DmaClaSrcSelRegs</name>
         <value>0x7980</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-470">
         <name>_AnalogSubsysRegs</name>
         <value>0x5d700</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-471">
         <name>_Cmpss3Regs</name>
         <value>0x5cc0</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-472">
         <name>_DcsmBank0Z1Regs</name>
         <value>0x5f000</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-473">
         <name>_PieCtrlRegs</name>
         <value>0xce0</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-474">
         <name>_DevCfgRegs</name>
         <value>0x5d000</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-475">
         <name>_EradCounter2Regs</name>
         <value>0x5e990</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-476">
         <name>_SpibRegs</name>
         <value>0x6110</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-477">
         <name>_Clb1LogicCtrlRegs</name>
         <value>0x3100</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-478">
         <name>_Pga7Regs</name>
         <value>0x5b60</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-479">
         <name>_AdccRegs</name>
         <value>0x7500</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-47a">
         <name>_ECap1Regs</name>
         <value>0x5200</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-47b">
         <name>_OutputXbarRegs</name>
         <value>0x7a80</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-47c">
         <name>_LinaRegs</name>
         <value>0x6a00</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-47d">
         <name>_MemoryErrorRegs</name>
         <value>0x5f500</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-47e">
         <name>_Cla1Regs</name>
         <value>0x1400</value>
         <object_component_ref idref="oc-10a"/>
      </symbol>
      <symbol id="sm-47f">
         <name>_EQep1Regs</name>
         <value>0x5100</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-480">
         <name>_DcsmBank1Z2Regs</name>
         <value>0x5f140</value>
         <object_component_ref idref="oc-f5"/>
      </symbol>
      <symbol id="sm-481">
         <name>_DacbRegs</name>
         <value>0x5c10</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-482">
         <name>_Clb2DataExchRegs</name>
         <value>0x3600</value>
         <object_component_ref idref="oc-123"/>
      </symbol>
      <symbol id="sm-483">
         <name>_Clb4LogicCfgRegs</name>
         <value>0x3c00</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-484">
         <name>_UidRegs</name>
         <value>0x703cc</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-485">
         <name>_EradGlobalRegs</name>
         <value>0x5e800</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-486">
         <name>_EPwm1Regs</name>
         <value>0x4000</value>
         <object_component_ref idref="oc-11a"/>
      </symbol>
      <symbol id="sm-487">
         <name>_EradCounter3Regs</name>
         <value>0x5e9a0</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-488">
         <name>_Clb3LogicCtrlRegs</name>
         <value>0x3900</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-489">
         <name>_Pga6Regs</name>
         <value>0x5b50</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-48a">
         <name>_AdcbRegs</name>
         <value>0x7480</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-48b">
         <name>_ScibRegs</name>
         <value>0x7210</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-48c">
         <name>_AdcaResultRegs</name>
         <value>0xb00</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-48d">
         <name>_ECap2Regs</name>
         <value>0x5240</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-48e">
         <name>_EradHWBP3Regs</name>
         <value>0x5e910</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-48f">
         <name>_AdcbResultRegs</name>
         <value>0xb20</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-490">
         <name>_Clb1LogicCfgRegs</name>
         <value>0x3000</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-491">
         <name>_AdccResultRegs</name>
         <value>0xb40</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-492">
         <name>_EQep2Regs</name>
         <value>0x5140</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-493">
         <name>_Cmpss1Regs</name>
         <value>0x5c80</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-494">
         <name>_DcsmBank1Z1Regs</name>
         <value>0x5f100</value>
         <object_component_ref idref="oc-f4"/>
      </symbol>
      <symbol id="sm-495">
         <name>_EPwm8Regs</name>
         <value>0x4700</value>
         <object_component_ref idref="oc-121"/>
      </symbol>
      <symbol id="sm-496">
         <name>_DcsmCommonRegs</name>
         <value>0x5f070</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-497">
         <name>_EPwm2Regs</name>
         <value>0x4100</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-498">
         <name>_Pga1Regs</name>
         <value>0x5b00</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-499">
         <name>_ECap3Regs</name>
         <value>0x5280</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-49a">
         <name>_EradHWBP2Regs</name>
         <value>0x5e908</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-49b">
         <name>_ClaPromCrc0Regs</name>
         <value>0x61c0</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-49c">
         <name>_SyncSocRegs</name>
         <value>0x7940</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-49d">
         <name>_NmiIntruptRegs</name>
         <value>0x7060</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-49e">
         <name>_FsiRxaRegs</name>
         <value>0x6680</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-49f">
         <name>_EPwm3Regs</name>
         <value>0x4200</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-4a0">
         <name>_EradCounter1Regs</name>
         <value>0x5e980</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-4a1">
         <name>_EradHWBP1Regs</name>
         <value>0x5e900</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-4a2">
         <name>_I2caRegs</name>
         <value>0x7300</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-4b3">
         <name>_ADC_setOffsetTrimAll</name>
         <value>0xf722</value>
         <object_component_ref idref="oc-1b8"/>
      </symbol>
      <symbol id="sm-4e3">
         <name>_CMPSS_configLatchOnPWMSYNC</name>
         <value>0xf6ce</value>
         <object_component_ref idref="oc-1bc"/>
      </symbol>
      <symbol id="sm-4e5">
         <name>_CMPSS_configFilterLow</name>
         <value>0xf3cf</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-4e7">
         <name>_CMPSS_configFilterHigh</name>
         <value>0xf386</value>
         <object_component_ref idref="oc-1b9"/>
      </symbol>
      <symbol id="sm-4e9">
         <name>_CMPSS_configRamp</name>
         <value>0xf461</value>
         <object_component_ref idref="oc-1bb"/>
      </symbol>
      <symbol id="sm-54a">
         <name>_DCC_verifyClockFrequency</name>
         <value>0xdb46</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-57b">
         <name>_GPIO_setAnalogMode</name>
         <value>0xf52b</value>
         <object_component_ref idref="oc-1b3"/>
      </symbol>
      <symbol id="sm-57d">
         <name>_GPIO_setControllerCore</name>
         <value>0xdfc8</value>
         <object_component_ref idref="oc-1be"/>
      </symbol>
      <symbol id="sm-57f">
         <name>_GPIO_setPinConfig</name>
         <value>0xf565</value>
         <object_component_ref idref="oc-1b6"/>
      </symbol>
      <symbol id="sm-581">
         <name>_GPIO_setDirectionMode</name>
         <value>0xf640</value>
         <object_component_ref idref="oc-1b2"/>
      </symbol>
      <symbol id="sm-583">
         <name>_GPIO_setQualificationMode</name>
         <value>0xf59c</value>
         <object_component_ref idref="oc-1b7"/>
      </symbol>
      <symbol id="sm-585">
         <name>_GPIO_setPadConfig</name>
         <value>0xf334</value>
         <object_component_ref idref="oc-1b1"/>
      </symbol>
      <symbol id="sm-5b4">
         <name>_Interrupt_illegalOperationHandler</name>
         <value>0xfb08</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-5b6">
         <name>_Interrupt_enable</name>
         <value>0xf5d3</value>
         <object_component_ref idref="oc-1bf"/>
      </symbol>
      <symbol id="sm-5b8">
         <name>_Interrupt_initVectorTable</name>
         <value>0xf85b</value>
         <object_component_ref idref="oc-1a8"/>
      </symbol>
      <symbol id="sm-5ba">
         <name>_Interrupt_initModule</name>
         <value>0xf4ee</value>
         <object_component_ref idref="oc-1a7"/>
      </symbol>
      <symbol id="sm-5bc">
         <name>_Interrupt_nmiHandler</name>
         <value>0xfb12</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-5be">
         <name>_Interrupt_defaultHandler</name>
         <value>0xf98f</value>
         <object_component_ref idref="oc-147"/>
      </symbol>
      <symbol id="sm-661">
         <name>_SysCtl_selectXTALSingleEnded</name>
         <value>0xf7ff</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-663">
         <name>_SysCtl_selectXTAL</name>
         <value>0xf60a</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-665">
         <name>_SysCtl_delay</name>
         <value>0xf6</value>
         <object_component_ref idref="oc-150"/>
      </symbol>
      <symbol id="sm-666">
         <name>_SysCtl_selectOscSource</name>
         <value>0xf418</value>
         <object_component_ref idref="oc-1c8"/>
      </symbol>
      <symbol id="sm-668">
         <name>_SysCtl_isPLLValid</name>
         <value>0xdcd1</value>
         <object_component_ref idref="oc-1c9"/>
      </symbol>
      <symbol id="sm-66a">
         <name>_SysCtl_setClock</name>
         <value>0xdc11</value>
         <object_component_ref idref="oc-1ae"/>
      </symbol>
      <symbol id="sm-66c">
         <name>_SysCtl_getLowSpeedClock</name>
         <value>0xf906</value>
         <object_component_ref idref="oc-1b0"/>
      </symbol>
      <symbol id="sm-66e">
         <name>_SysCtl_getClock</name>
         <value>0xf229</value>
         <object_component_ref idref="oc-1af"/>
      </symbol>
      <symbol id="sm-681">
         <name>_XBAR_setOutputMuxConfig</name>
         <value>0xf6a0</value>
         <object_component_ref idref="oc-1c0"/>
      </symbol>
      <symbol id="sm-683">
         <name>_XBAR_setEPWMMuxConfig</name>
         <value>0xf671</value>
         <object_component_ref idref="oc-1bd"/>
      </symbol>
      <symbol id="sm-691">
         <name>_PID_GRANDO_F_FUNC</name>
         <value>0xddd6</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-692">
         <name>_PID_GRANDO_F_init</name>
         <value>0xdd8e</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-69d">
         <name>_DQ0_ABC_F_FUNC</name>
         <value>0xf29d</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-69e">
         <name>_DQ0_ABC_F_init</name>
         <value>0xf287</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-6a9">
         <name>_CNTL_2P2Z_F_VARS_init</name>
         <value>0xdece</value>
         <object_component_ref idref="oc-165"/>
      </symbol>
      <symbol id="sm-6aa">
         <name>_CNTL_2P2Z_F_COEFFS_init</name>
         <value>0xdf38</value>
         <object_component_ref idref="oc-165"/>
      </symbol>
      <symbol id="sm-6ab">
         <name>_CNTL_2P2Z_F_FUNC</name>
         <value>0xdee4</value>
         <object_component_ref idref="oc-165"/>
      </symbol>
      <symbol id="sm-6c6">
         <name>_c_int00</name>
         <value>0xf2de</value>
         <object_component_ref idref="oc-16a"/>
      </symbol>
      <symbol id="sm-6c7">
         <name>__stack</name>
         <value>0x400</value>
         <object_component_ref idref="oc-16b"/>
      </symbol>
      <symbol id="sm-6d8">
         <name>FS$$DIV</name>
         <value>0xde46</value>
         <object_component_ref idref="oc-171"/>
      </symbol>
      <symbol id="sm-6e6">
         <name>I$$MOD</name>
         <value>0xf7ee</value>
         <object_component_ref idref="oc-176"/>
      </symbol>
      <symbol id="sm-6e7">
         <name>I$$DIV</name>
         <value>0xf7dd</value>
         <object_component_ref idref="oc-176"/>
      </symbol>
      <symbol id="sm-6f6">
         <name>_copy_in</name>
         <value>0xf7b9</value>
         <object_component_ref idref="oc-1a2"/>
      </symbol>
      <symbol id="sm-708">
         <name>_memcpy</name>
         <value>0xf879</value>
         <object_component_ref idref="oc-1ab"/>
      </symbol>
      <symbol id="sm-715">
         <name>__system_pre_init</name>
         <value>0xfb34</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-723">
         <name>__system_post_cinit</name>
         <value>0xdfff</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-738">
         <name>C$$EXIT</name>
         <value>0xf6f9</value>
         <object_component_ref idref="oc-1a5"/>
      </symbol>
      <symbol id="sm-739">
         <name>_exit</name>
         <value>0xf6fb</value>
         <object_component_ref idref="oc-1a5"/>
      </symbol>
      <symbol id="sm-73b">
         <name>___TI_cleanup_ptr</name>
         <value>0xe84e</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-73c">
         <name>___TI_enable_exit_profile_output</name>
         <value>0xe84c</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-73d">
         <name>_abort</name>
         <value>0xf6f9</value>
         <object_component_ref idref="oc-1a5"/>
      </symbol>
      <symbol id="sm-73f">
         <name>___TI_dtors_ptr</name>
         <value>0xe850</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-753">
         <name>__unlock</name>
         <value>0xe858</value>
         <object_component_ref idref="oc-1dc"/>
      </symbol>
      <symbol id="sm-754">
         <name>__lock</name>
         <value>0xe856</value>
         <object_component_ref idref="oc-1ac"/>
      </symbol>
      <symbol id="sm-755">
         <name>__register_lock</name>
         <value>0xfb20</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-757">
         <name>__nop</name>
         <value>0xfb24</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-759">
         <name>__register_unlock</name>
         <value>0xfb1c</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-769">
         <name>__args_main</name>
         <value>0xfab1</value>
         <object_component_ref idref="oc-1a4"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
