# 三相逆变器波形改进说明

## 改进概述

基于您提供的示波器波形，我们对三相逆变器系统进行了以下改进，以提升输出波形质量：

## 主要改进内容

### 1. **直接改善现有波形质量** ⭐
- **改进SPWM算法**: 重写了三相正弦波生成逻辑，确保精确的120度相位差
- **优化占空比计算**: 改用更精确的占空比计算方法，避免过调制
- **调制深度优化**: 将默认调制深度从0.99降低到0.8，避免波形失真
- **相位精度提升**: 使用更精确的时间基准和相位计算

### 2. **改进闭环控制算法**
- **优化PR控制器**: 改进了比例谐振控制器的参考信号生成
- **精确相位控制**: 确保三相输出的120度相位差
- **占空比限制**: 添加了5%-95%的占空比限制，防止饱和

### 3. **添加死区时间配置**
- **目的**: 防止上下桥臂同时导通，保护功率器件
- **实现**: 添加了1000ns的死区时间
- **代码位置**: `ApplyDeadTime()` 函数

### 4. **实现SVPWM调制方式**
- **目的**: 相比传统SPWM，SVPWM可以提供更好的直流电压利用率和更低的谐波失真
- **优势**:
  - 直流电压利用率提高15.47%
  - 降低输出电压谐波含量
  - 减少开关损耗
- **代码位置**: `SVPWM_Calculate()` 函数

### 5. **动态调制深度调整**
- **功能**: 可以实时调整调制深度，优化波形质量
- **范围**: 10%-95%可调
- **显示**: OLED右上角显示当前调制深度百分比

### 6. **数字滤波器**
- **目的**: 进一步改善PWM波形质量，减少高频噪声
- **实现**: 一阶低通滤波器，滤波系数α=0.1
- **应用**: 仅在SVPWM模式下使用

## 操作说明

### 按键功能
- **模式1（开环）**:
  - **单独按GPIO16**: 增加调制深度（+5%）
  - **单独按GPIO17**: 减少调制深度（-5%）
  - **同时按GPIO16+GPIO17**: 切换调制模式（SPWM/SVPWM）
- **模式2（闭环）**:
  - **GPIO16**: 电压增加
  - **GPIO17**: 电压减少
- **模式切换**:
  - **GPIO56**: 切换到模式1（开环频率控制）
  - **GPIO57**: 切换到模式2（闭环电压控制）

### OLED显示
- **第1行左**: 参考电压 (Vref)
- **第1行右**: 调制深度百分比 (10-95%)
- **第2行**: BC相电压有效值
- **第3行**: 输出频率
- **第4行左**: 工作模式 (1=开环, 2=闭环)
- **第4行右**: 调制类型 (0=SPWM, 1=SVPWM)

## 预期改善效果

### 🎯 **立即可见的改善**（现有功能优化）
1. **更平滑的PWM波形**: 改进的占空比计算算法
2. **精确的三相对称性**: 确保120度相位差
3. **减少波形失真**: 优化的调制深度和占空比限制
4. **更稳定的输出**: 改进的闭环控制算法

### 🚀 **SVPWM相比SPWM的优势**（新增功能）
1. **更高的电压利用率**: 在相同直流母线电压下，输出电压幅值提高约15%
2. **更低的谐波失真**: 特别是低次谐波含量显著降低
3. **更好的波形质量**: 输出电压更接近理想正弦波
4. **更低的开关损耗**: 减少不必要的开关动作

### 🛡️ **系统保护和可靠性**
1. **死区时间保护**: 防止上下桥臂直通
2. **占空比限制**: 防止过调制和饱和
3. **调制深度限制**: 避免波形失真
4. **参数范围保护**: 防止异常参数设置

### 📊 **可调节性增强**
1. **实时调制深度调整**: 可根据负载优化波形
2. **调制模式切换**: 可对比不同调制方式的效果
3. **参数可视化**: OLED实时显示关键参数

## 建议的测试步骤

1. **编译并下载程序**到DSP
2. **启动系统**，默认为模式1，SPWM调制
3. **观察示波器波形**，记录SPWM模式下的波形质量
4. **切换到SVPWM模式**（同时按GPIO16+GPIO17）
5. **对比波形质量**，应该能看到明显改善
6. **测试不同频率**下的波形表现
7. **切换到模式2**测试闭环控制性能

## 进一步优化建议

如果需要进一步改善波形质量，可以考虑：

1. **提高PWM频率**: 从20kHz提高到更高频率
2. **优化控制器参数**: 调整PR控制器和PID控制器参数
3. **添加输出滤波器**: 硬件LC滤波器
4. **实现更高级的调制策略**: 如不连续SVPWM等
5. **优化死区时间**: 根据实际功率器件特性调整

## 注意事项

1. 死区时间设置需要根据实际使用的功率器件特性调整
2. SVPWM算法中的扇区判断需要确保正确性
3. 滤波器参数可能需要根据实际应用调整
4. 建议在低功率下先测试，确认无误后再应用到高功率场合
