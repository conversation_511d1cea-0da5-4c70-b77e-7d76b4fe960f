******************************************************************************
             TMS320C2000 Linker PC v22.6.0                     
******************************************************************************
>> Linked Thu Jul 10 20:38:41 2025

OUTPUT FILE NAME:   <F280049_2024_ADC_Ram.out>
ENTRY POINT SYMBOL: "code_start"  address: ********


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  BEGIN                 ********   ********  ********  ********  RWIX
  RAMM0                 000000f6   0000030a  ********  ********  RWIX
  RAMLS0                ********   ********  ********  ********  RWIX
  RAMLS1                ********   ********  ********  ********  RWIX
  RAMLS2                ********   ********  ********  ********  RWIX
  RAMLS3                ********   ********  ********  ********  RWIX
  RAMLS4                0000a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC5      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC6      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC7      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC8      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC9      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC10     0008a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC11     0008b000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC12     0008c000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC13     0008d000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC14     0008e000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC15     0008f000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC5      00095000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC6      00096000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC7      00097000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC8      00098000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC9      00099000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC10     0009a000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC11     0009b000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC12     0009c000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC13     0009d000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC14     0009e000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC15     0009f000   ********  ********  ********  RWIX
  RESET                 003fffc0   ********  ********  ********  RWIX

PAGE 1:
  BOOT_RSVD             ********   000000f1  ********  000000f1  RWIX
  RAMM1                 00000400   000003f8  ********  000002f8  RWIX
  ADCARESULT            00000b00   00000018  00000018  ********  RWIX
  ADCBRESULT            00000b20   00000018  00000018  ********  RWIX
  ADCCRESULT            00000b40   00000018  00000018  ********  RWIX
  CPUTIMER0             00000c00   ********  ********  ********  RWIX
  CPUTIMER1             00000c08   ********  ********  ********  RWIX
  CPUTIMER2             00000c10   ********  ********  ********  RWIX
  PIECTRL               00000ce0   0000001a  0000001a  ********  RWIX
  PIEVECTTABLE          00000d00   00000200  000001c0  ********  RWIX
  DMA                   ********   00000200  000000e0  00000120  RWIX
  CLA1                  00001400   ********  00000048  00000038  RWIX
  CLB1LOGICCFG          00003000   00000052  00000050  ********  RWIX
  CLB1LOGICCTRL         00003100   ********  ********  ********  RWIX
  CLB1DATAEXCH          00003200   00000200  00000108  000000f8  RWIX
  CLB2LOGICCFG          00003400   00000052  00000050  ********  RWIX
  CLB2LOGICCTRL         00003500   ********  ********  ********  RWIX
  CLB2DATAEXCH          00003600   00000200  00000108  000000f8  RWIX
  CLB3LOGICCFG          00003800   00000052  00000050  ********  RWIX
  CLB3LOGICCTRL         00003900   ********  ********  ********  RWIX
  CLB3DATAEXCH          00003a00   00000200  00000108  000000f8  RWIX
  CLB4LOGICCFG          00003c00   00000052  00000050  ********  RWIX
  CLB4LOGICCTRL         00003d00   ********  ********  ********  RWIX
  CLB4DATAEXCH          00003e00   00000200  00000108  000000f8  RWIX
  EPWM1                 00004000   ********  ********  ********  RWIX
  EPWM2                 00004100   ********  ********  ********  RWIX
  EPWM3                 00004200   ********  ********  ********  RWIX
  EPWM4                 00004300   ********  ********  ********  RWIX
  EPWM5                 00004400   ********  ********  ********  RWIX
  EPWM6                 00004500   ********  ********  ********  RWIX
  EPWM7                 00004600   ********  ********  ********  RWIX
  EPWM8                 00004700   ********  ********  ********  RWIX
  EQEP1                 00005100   ********  00000036  0000000a  RWIX
  EQEP2                 00005140   ********  00000036  0000000a  RWIX
  ECAP1                 ********   0000001e  0000001a  ********  RWIX
  ECAP2                 ********   0000001e  0000001a  ********  RWIX
  ECAP3                 ********   0000001e  0000001a  ********  RWIX
  ECAP4                 000052c0   0000001e  0000001a  ********  RWIX
  ECAP5                 ********   0000001e  0000001a  ********  RWIX
  ECAP6                 ********   0000001e  0000001a  ********  RWIX
  HRCAP6                00005360   ********  00000016  0000000a  RWIX
  ECAP7                 ********   0000001e  0000001a  ********  RWIX
  HRCAP7                000053a0   ********  00000016  0000000a  RWIX
  PGA1                  00005b00   ********  0000000a  ********  RWIX
  PGA2                  00005b10   ********  0000000a  ********  RWIX
  PGA3                  00005b20   ********  0000000a  ********  RWIX
  PGA4                  00005b30   ********  0000000a  ********  RWIX
  PGA5                  00005b40   ********  0000000a  ********  RWIX
  PGA6                  00005b50   ********  0000000a  ********  RWIX
  PGA7                  00005b60   ********  0000000a  ********  RWIX
  DACA                  00005c00   ********  00000007  00000001  RWIX
  DACB                  00005c10   ********  00000007  00000001  RWIX
  CMPSS1                00005c80   ********  0000001b  00000005  RWIX
  CMPSS2                00005ca0   ********  0000001b  00000005  RWIX
  CMPSS3                00005cc0   ********  0000001b  00000005  RWIX
  CMPSS4                00005ce0   ********  0000001b  00000005  RWIX
  CMPSS5                00005d00   ********  0000001b  00000005  RWIX
  CMPSS6                00005d20   ********  0000001b  00000005  RWIX
  CMPSS7                00005d40   ********  0000001b  00000005  RWIX
  SDFM1                 00005e00   00000070  00000050  ********  RWIX
  SPIA                  00006100   ********  ********  ********  RWIX
  SPIB                  00006110   ********  ********  ********  RWIX
  CLAPROMCRC0           000061c0   ********  ********  ********  RWIX
  PMBUSA                ********   ********  0000001e  ********  RWIX
  FSITXA                00006600   00000050  00000050  ********  RWIX
  FSIRXA                00006680   00000050  00000050  ********  RWIX
  LINA                  00006a00   000000ec  000000ec  ********  RWIX
  WD                    00007000   0000002c  0000002b  00000001  RWIX
  NMIINTRUPT            00007060   ********  00000007  00000001  RWIX
  XINT                  00007070   0000000c  0000000b  00000001  RWIX
  SCIA                  00007200   ********  ********  ********  RWIX
  SCIB                  00007210   ********  ********  ********  RWIX
  I2CA                  00007300   ********  ********  ********  RWIX
  ADCA                  00007400   ********  ********  0000000a  RWIX
  ADCB                  00007480   ********  ********  0000000a  RWIX
  ADCC                  00007500   ********  ********  0000000a  RWIX
  INPUTXBAR             00007900   ********  ********  ********  RWIX
  XBAR                  00007920   ********  ********  ********  RWIX
  SYNCSOC               00007940   ********  ********  ********  RWIX
  DMACLASRCSEL          ********   0000001a  0000001a  ********  RWIX
  EPWMXBAR              00007a00   ********  ********  ********  RWIX
  CLBXBAR               00007a40   ********  ********  ********  RWIX
  OUTPUTXBAR            00007a80   ********  ********  ********  RWIX
  GPIOCTRL              00007c00   00000200  00000200  ********  RWIX
  GPIODATA              00007f00   ********  0000003a  ********  RWIX
  RAMLS5                0000a800   ********  00000535  000002cb  RWIX
  RAMLS6                0000b000   ********  ********  ********  RWIX
  RAMLS7                0000b800   ********  ********  ********  RWIX
  RAMGS0                0000c000   00002000  00002000  ********  RWIX
  RAMGS1                0000e000   00002000  00001427  00000bd9  RWIX
  RAMGS2                00010000   00002000  ********  00002000  RWIX
  RAMGS3                00012000   00001ff8  ********  00001ff8  RWIX
  CANA                  00048000   00000200  00000164  0000009c  RWIX
  CANB                  0004a000   00000200  00000164  0000009c  RWIX
  DEVCFG                0005d000   00000180  00000132  0000004e  RWIX
  CLKCFG                0005d200   00000036  00000034  ********  RWIX
  CPUSYS                0005d300   00000082  00000082  ********  RWIX
  SYSPERIPHAC           0005d500   00000200  00000200  ********  RWIX
  ANALOGSUBSYS          0005d700   00000090  00000090  ********  RWIX
  DCC0                  0005e700   0000002c  0000002c  ********  RWIX
  ERADGLOBAL            0005e800   00000014  0000000b  00000009  RWIX
  ERADHWBP1             0005e900   ********  ********  ********  RWIX
  ERADHWBP2             0005e908   ********  ********  ********  RWIX
  ERADHWBP3             0005e910   ********  ********  ********  RWIX
  ERADHWBP4             0005e918   ********  ********  ********  RWIX
  ERADHWBP5             0005e920   ********  ********  ********  RWIX
  ERADHWBP6             0005e928   ********  ********  ********  RWIX
  ERADHWBP7             0005e930   ********  ********  ********  RWIX
  ERADHWBP8             0005e938   ********  ********  ********  RWIX
  ERADCOUNTER1          0005e980   ********  0000000a  ********  RWIX
  ERADCOUNTER2          0005e990   ********  0000000a  ********  RWIX
  ERADCOUNTER3          0005e9a0   ********  0000000a  ********  RWIX
  ERADCOUNTER4          0005e9b0   ********  0000000a  ********  RWIX
  DCSMBANK0Z1           0005f000   ********  ********  ********  RWIX
  DCSMBANK0Z2           0005f040   ********  ********  ********  RWIX
  DCSMCOMMON            0005f070   ********  ********  ********  RWIX
  DCSMBANK1Z1           0005f100   ********  ********  ********  RWIX
  DCSMBANK1Z2           0005f140   ********  ********  ********  RWIX
  MEMCFG                0005f400   ********  ********  0000000a  RWIX
  ACCESSPROTECTION      0005f4c0   ********  0000002e  ********  RWIX
  MEMORYERROR           0005f500   ********  0000003a  ********  RWIX
  FLASH0CTRL            0005f800   ********  ********  ********  RWIX
  FLASH0ECC             0005fb00   ********  ********  ********  RWIX
  UID                   000703cc   ********  ********  ********  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
codestart 
*          0    ********    ********     
                  ********    ********     f28004x_codestartbranch.obj (codestart)

.TI.ramfunc 
*          0    000000f6    ********     
                  000000f6    ********     driverlib_coff.lib : sysctl.obj (.TI.ramfunc)

.cinit     1    0000e000    ********     
                  0000e000    000005f3     OLED.obj (.cinit:_F8X16)
                  0000e5f3    0000022b     OLED.obj (.cinit:_F6x8)
                  0000e81e    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  0000e82c    0000000a     device.obj (.cinit)
                  0000e836    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  0000e83b    00000005                       : _lock.c.obj (.cinit:__unlock)
                  0000e840    00000005     lab_main.obj (.cinit)
                  0000e845    ********     --HOLE-- [fill = 0]

.reset     0    003fffc0    ********     DSECT
                  003fffc0    ********     rts2800_fpu32.lib : boot28.asm.obj (.reset)

.stack     1    00000400    ********     UNINITIALIZED
                  00000400    ********     --HOLE--

.pinit     0    000000f6    ********     UNINITIALIZED

AdcaResultRegsFile 
*          1    00000b00    00000018     UNINITIALIZED
                  00000b00    00000018     f28004x_globalvariabledefs.obj (AdcaResultRegsFile)

AdcbResultRegsFile 
*          1    00000b20    00000018     UNINITIALIZED
                  00000b20    00000018     f28004x_globalvariabledefs.obj (AdcbResultRegsFile)

AdccResultRegsFile 
*          1    00000b40    00000018     UNINITIALIZED
                  00000b40    00000018     f28004x_globalvariabledefs.obj (AdccResultRegsFile)

CpuTimer0RegsFile 
*          1    00000c00    ********     UNINITIALIZED
                  00000c00    ********     f28004x_globalvariabledefs.obj (CpuTimer0RegsFile)

CpuTimer1RegsFile 
*          1    00000c08    ********     UNINITIALIZED
                  00000c08    ********     f28004x_globalvariabledefs.obj (CpuTimer1RegsFile)

CpuTimer2RegsFile 
*          1    00000c10    ********     UNINITIALIZED
                  00000c10    ********     f28004x_globalvariabledefs.obj (CpuTimer2RegsFile)

PieCtrlRegsFile 
*          1    00000ce0    0000001a     UNINITIALIZED
                  00000ce0    0000001a     f28004x_globalvariabledefs.obj (PieCtrlRegsFile)

DmaRegsFile 
*          1    ********    000000e0     UNINITIALIZED
                  ********    000000e0     f28004x_globalvariabledefs.obj (DmaRegsFile)

Cla1RegsFile 
*          1    00001400    00000048     UNINITIALIZED
                  00001400    00000048     f28004x_globalvariabledefs.obj (Cla1RegsFile)

Clb1LogicCfgRegsFile 
*          1    00003000    00000050     UNINITIALIZED
                  00003000    00000050     f28004x_globalvariabledefs.obj (Clb1LogicCfgRegsFile)

Clb1LogicCtrlRegsFile 
*          1    00003100    ********     UNINITIALIZED
                  00003100    ********     f28004x_globalvariabledefs.obj (Clb1LogicCtrlRegsFile)

Clb1DataExchRegsFile 
*          1    00003200    00000108     UNINITIALIZED
                  00003200    00000108     f28004x_globalvariabledefs.obj (Clb1DataExchRegsFile)

Clb2LogicCfgRegsFile 
*          1    00003400    00000050     UNINITIALIZED
                  00003400    00000050     f28004x_globalvariabledefs.obj (Clb2LogicCfgRegsFile)

Clb2LogicCtrlRegsFile 
*          1    00003500    ********     UNINITIALIZED
                  00003500    ********     f28004x_globalvariabledefs.obj (Clb2LogicCtrlRegsFile)

Clb2DataExchRegsFile 
*          1    00003600    00000108     UNINITIALIZED
                  00003600    00000108     f28004x_globalvariabledefs.obj (Clb2DataExchRegsFile)

Clb3LogicCfgRegsFile 
*          1    00003800    00000050     UNINITIALIZED
                  00003800    00000050     f28004x_globalvariabledefs.obj (Clb3LogicCfgRegsFile)

Clb3LogicCtrlRegsFile 
*          1    00003900    ********     UNINITIALIZED
                  00003900    ********     f28004x_globalvariabledefs.obj (Clb3LogicCtrlRegsFile)

Clb3DataExchRegsFile 
*          1    00003a00    00000108     UNINITIALIZED
                  00003a00    00000108     f28004x_globalvariabledefs.obj (Clb3DataExchRegsFile)

Clb4LogicCfgRegsFile 
*          1    00003c00    00000050     UNINITIALIZED
                  00003c00    00000050     f28004x_globalvariabledefs.obj (Clb4LogicCfgRegsFile)

Clb4LogicCtrlRegsFile 
*          1    00003d00    ********     UNINITIALIZED
                  00003d00    ********     f28004x_globalvariabledefs.obj (Clb4LogicCtrlRegsFile)

Clb4DataExchRegsFile 
*          1    00003e00    00000108     UNINITIALIZED
                  00003e00    00000108     f28004x_globalvariabledefs.obj (Clb4DataExchRegsFile)

EPwm1RegsFile 
*          1    00004000    ********     UNINITIALIZED
                  00004000    ********     f28004x_globalvariabledefs.obj (EPwm1RegsFile)

EPwm2RegsFile 
*          1    00004100    ********     UNINITIALIZED
                  00004100    ********     f28004x_globalvariabledefs.obj (EPwm2RegsFile)

EPwm3RegsFile 
*          1    00004200    ********     UNINITIALIZED
                  00004200    ********     f28004x_globalvariabledefs.obj (EPwm3RegsFile)

EPwm4RegsFile 
*          1    00004300    ********     UNINITIALIZED
                  00004300    ********     f28004x_globalvariabledefs.obj (EPwm4RegsFile)

EPwm5RegsFile 
*          1    00004400    ********     UNINITIALIZED
                  00004400    ********     f28004x_globalvariabledefs.obj (EPwm5RegsFile)

EPwm6RegsFile 
*          1    00004500    ********     UNINITIALIZED
                  00004500    ********     f28004x_globalvariabledefs.obj (EPwm6RegsFile)

EPwm7RegsFile 
*          1    00004600    ********     UNINITIALIZED
                  00004600    ********     f28004x_globalvariabledefs.obj (EPwm7RegsFile)

EPwm8RegsFile 
*          1    00004700    ********     UNINITIALIZED
                  00004700    ********     f28004x_globalvariabledefs.obj (EPwm8RegsFile)

EQep1RegsFile 
*          1    00005100    00000036     UNINITIALIZED
                  00005100    00000036     f28004x_globalvariabledefs.obj (EQep1RegsFile)

EQep2RegsFile 
*          1    00005140    00000036     UNINITIALIZED
                  00005140    00000036     f28004x_globalvariabledefs.obj (EQep2RegsFile)

ECap1RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap1RegsFile)

ECap2RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap2RegsFile)

ECap3RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap3RegsFile)

ECap4RegsFile 
*          1    000052c0    0000001a     UNINITIALIZED
                  000052c0    0000001a     f28004x_globalvariabledefs.obj (ECap4RegsFile)

ECap5RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap5RegsFile)

ECap6RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap6RegsFile)

HRCap6RegsFile 
*          1    00005360    00000016     UNINITIALIZED
                  00005360    00000016     f28004x_globalvariabledefs.obj (HRCap6RegsFile)

ECap7RegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (ECap7RegsFile)

HRCap7RegsFile 
*          1    000053a0    00000016     UNINITIALIZED
                  000053a0    00000016     f28004x_globalvariabledefs.obj (HRCap7RegsFile)

Pga1RegsFile 
*          1    00005b00    0000000a     UNINITIALIZED
                  00005b00    0000000a     f28004x_globalvariabledefs.obj (Pga1RegsFile)

Pga2RegsFile 
*          1    00005b10    0000000a     UNINITIALIZED
                  00005b10    0000000a     f28004x_globalvariabledefs.obj (Pga2RegsFile)

Pga3RegsFile 
*          1    00005b20    0000000a     UNINITIALIZED
                  00005b20    0000000a     f28004x_globalvariabledefs.obj (Pga3RegsFile)

Pga4RegsFile 
*          1    00005b30    0000000a     UNINITIALIZED
                  00005b30    0000000a     f28004x_globalvariabledefs.obj (Pga4RegsFile)

Pga5RegsFile 
*          1    00005b40    0000000a     UNINITIALIZED
                  00005b40    0000000a     f28004x_globalvariabledefs.obj (Pga5RegsFile)

Pga6RegsFile 
*          1    00005b50    0000000a     UNINITIALIZED
                  00005b50    0000000a     f28004x_globalvariabledefs.obj (Pga6RegsFile)

Pga7RegsFile 
*          1    00005b60    0000000a     UNINITIALIZED
                  00005b60    0000000a     f28004x_globalvariabledefs.obj (Pga7RegsFile)

DacaRegsFile 
*          1    00005c00    00000007     UNINITIALIZED
                  00005c00    00000007     f28004x_globalvariabledefs.obj (DacaRegsFile)

DacbRegsFile 
*          1    00005c10    00000007     UNINITIALIZED
                  00005c10    00000007     f28004x_globalvariabledefs.obj (DacbRegsFile)

Cmpss1RegsFile 
*          1    00005c80    0000001b     UNINITIALIZED
                  00005c80    0000001b     f28004x_globalvariabledefs.obj (Cmpss1RegsFile)

Cmpss2RegsFile 
*          1    00005ca0    0000001b     UNINITIALIZED
                  00005ca0    0000001b     f28004x_globalvariabledefs.obj (Cmpss2RegsFile)

Cmpss3RegsFile 
*          1    00005cc0    0000001b     UNINITIALIZED
                  00005cc0    0000001b     f28004x_globalvariabledefs.obj (Cmpss3RegsFile)

Cmpss4RegsFile 
*          1    00005ce0    0000001b     UNINITIALIZED
                  00005ce0    0000001b     f28004x_globalvariabledefs.obj (Cmpss4RegsFile)

Cmpss5RegsFile 
*          1    00005d00    0000001b     UNINITIALIZED
                  00005d00    0000001b     f28004x_globalvariabledefs.obj (Cmpss5RegsFile)

Cmpss6RegsFile 
*          1    00005d20    0000001b     UNINITIALIZED
                  00005d20    0000001b     f28004x_globalvariabledefs.obj (Cmpss6RegsFile)

Cmpss7RegsFile 
*          1    00005d40    0000001b     UNINITIALIZED
                  00005d40    0000001b     f28004x_globalvariabledefs.obj (Cmpss7RegsFile)

Sdfm1RegsFile 
*          1    00005e00    00000050     UNINITIALIZED
                  00005e00    00000050     f28004x_globalvariabledefs.obj (Sdfm1RegsFile)

SpiaRegsFile 
*          1    00006100    ********     UNINITIALIZED
                  00006100    ********     f28004x_globalvariabledefs.obj (SpiaRegsFile)

SpibRegsFile 
*          1    00006110    ********     UNINITIALIZED
                  00006110    ********     f28004x_globalvariabledefs.obj (SpibRegsFile)

ClaPromCrc0RegsFile 
*          1    000061c0    ********     UNINITIALIZED
                  000061c0    ********     f28004x_globalvariabledefs.obj (ClaPromCrc0RegsFile)

FsiTxaRegsFile 
*          1    00006600    00000050     UNINITIALIZED
                  00006600    00000050     f28004x_globalvariabledefs.obj (FsiTxaRegsFile)

FsiRxaRegsFile 
*          1    00006680    00000050     UNINITIALIZED
                  00006680    00000050     f28004x_globalvariabledefs.obj (FsiRxaRegsFile)

LinaRegsFile 
*          1    00006a00    000000ec     UNINITIALIZED
                  00006a00    000000ec     f28004x_globalvariabledefs.obj (LinaRegsFile)

WdRegsFile 
*          1    00007000    0000002b     UNINITIALIZED
                  00007000    0000002b     f28004x_globalvariabledefs.obj (WdRegsFile)

NmiIntruptRegsFile 
*          1    00007060    00000007     UNINITIALIZED
                  00007060    00000007     f28004x_globalvariabledefs.obj (NmiIntruptRegsFile)

XintRegsFile 
*          1    00007070    0000000b     UNINITIALIZED
                  00007070    0000000b     f28004x_globalvariabledefs.obj (XintRegsFile)

I2caRegsFile 
*          1    00007300    ********     UNINITIALIZED
                  00007300    ********     f28004x_globalvariabledefs.obj (I2caRegsFile)

AdcaRegsFile 
*          1    00007400    ********     UNINITIALIZED
                  00007400    ********     f28004x_globalvariabledefs.obj (AdcaRegsFile)

AdcbRegsFile 
*          1    00007480    ********     UNINITIALIZED
                  00007480    ********     f28004x_globalvariabledefs.obj (AdcbRegsFile)

AdccRegsFile 
*          1    00007500    ********     UNINITIALIZED
                  00007500    ********     f28004x_globalvariabledefs.obj (AdccRegsFile)

InputXbarRegsFile 
*          1    00007900    ********     UNINITIALIZED
                  00007900    ********     f28004x_globalvariabledefs.obj (InputXbarRegsFile)

XbarRegsFile 
*          1    00007920    ********     UNINITIALIZED
                  00007920    ********     f28004x_globalvariabledefs.obj (XbarRegsFile)

DmaClaSrcSelRegsFile 
*          1    ********    0000001a     UNINITIALIZED
                  ********    0000001a     f28004x_globalvariabledefs.obj (DmaClaSrcSelRegsFile)

EPwmXbarRegsFile 
*          1    00007a00    ********     UNINITIALIZED
                  00007a00    ********     f28004x_globalvariabledefs.obj (EPwmXbarRegsFile)

CLBXbarRegsFile 
*          1    00007a40    ********     UNINITIALIZED
                  00007a40    ********     f28004x_globalvariabledefs.obj (CLBXbarRegsFile)

OutputXbarRegsFile 
*          1    00007a80    ********     UNINITIALIZED
                  00007a80    ********     f28004x_globalvariabledefs.obj (OutputXbarRegsFile)

GpioCtrlRegsFile 
*          1    00007c00    00000200     UNINITIALIZED
                  00007c00    00000200     f28004x_globalvariabledefs.obj (GpioCtrlRegsFile)

GpioDataRegsFile 
*          1    00007f00    0000003a     UNINITIALIZED
                  00007f00    0000003a     f28004x_globalvariabledefs.obj (GpioDataRegsFile)

.econst    1    0000a800    00000535     
                  0000a800    0000019e     board.obj (.econst:.string)
                  0000a99e    0000009a     driverlib_coff.lib : sysctl.obj (.econst:.string)
                  0000aa38    00000098                        : gpio.obj (.econst:.string)
                  0000aad0    00000097                        : dcc.obj (.econst:.string)
                  0000ab67    00000001     --HOLE-- [fill = 0]
                  0000ab68    00000059     device.obj (.econst:.string)
                  0000abc1    00000001     --HOLE-- [fill = 0]
                  0000abc2    00000051     driverlib_coff.lib : interrupt.obj (.econst:.string)
                  0000ac13    00000001     --HOLE-- [fill = 0]
                  0000ac14    0000004d                        : cmpss.obj (.econst:.string)
                  0000ac61    00000001     --HOLE-- [fill = 0]
                  0000ac62    0000004b                        : adc.obj (.econst:.string)
                  0000acad    00000001     --HOLE-- [fill = 0]
                  0000acae    00000044     OLED.obj (.econst:.string)
                  0000acf2    00000043     lab_main.obj (.econst:.string)

.ebss      1    0000eb2c    000008fc     UNINITIALIZED
                  0000eb2c    ********     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  0000eb32    ********     device.obj (.ebss)
                  0000eb36    ********     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  0000eb38    ********                       : _lock.c.obj (.ebss:__unlock)
                  0000eb3a    ********     --HOLE--
                  0000eb40    000005f0     OLED.obj (.ebss:_F8X16)
                  0000f130    ********     --HOLE--
                  0000f140    00000228     OLED.obj (.ebss:_F6x8)
                  0000f368    00000018     --HOLE--
                  0000f380    000000a8     lab_main.obj (.ebss)

PieVectTableFile 
*          1    00000d00    000001c0     UNINITIALIZED
                  00000d00    000001c0     f28004x_globalvariabledefs.obj (PieVectTableFile)

EmuKeyVar 
*          1    00000d00    ********     UNINITIALIZED

EmuBModeVar 
*          1    00000d00    ********     UNINITIALIZED

EmuBootPinsVar 
*          1    00000d00    ********     UNINITIALIZED

FlashCallbackVar 
*          1    00000d00    ********     UNINITIALIZED

FlashScalingVar 
*          1    00000d00    ********     UNINITIALIZED

CanaRegsFile 
*          1    00048000    00000164     UNINITIALIZED
                  00048000    00000164     f28004x_globalvariabledefs.obj (CanaRegsFile)

CanbRegsFile 
*          1    0004a000    00000164     UNINITIALIZED
                  0004a000    00000164     f28004x_globalvariabledefs.obj (CanbRegsFile)

DevCfgRegsFile 
*          1    0005d000    00000132     UNINITIALIZED
                  0005d000    00000132     f28004x_globalvariabledefs.obj (DevCfgRegsFile)

ClkCfgRegsFile 
*          1    0005d200    00000034     UNINITIALIZED
                  0005d200    00000034     f28004x_globalvariabledefs.obj (ClkCfgRegsFile)

CpuSysRegsFile 
*          1    0005d300    00000082     UNINITIALIZED
                  0005d300    00000082     f28004x_globalvariabledefs.obj (CpuSysRegsFile)

SysPeriphAcRegsFile 
*          1    0005d500    00000200     UNINITIALIZED
                  0005d500    00000200     f28004x_globalvariabledefs.obj (SysPeriphAcRegsFile)

AnalogSubsysRegsFile 
*          1    0005d700    00000090     UNINITIALIZED
                  0005d700    00000090     f28004x_globalvariabledefs.obj (AnalogSubsysRegsFile)

Dcc0RegsFile 
*          1    0005e700    0000002c     UNINITIALIZED
                  0005e700    0000002c     f28004x_globalvariabledefs.obj (Dcc0RegsFile)

EradGlobalRegsFile 
*          1    0005e800    0000000b     UNINITIALIZED
                  0005e800    0000000b     f28004x_globalvariabledefs.obj (EradGlobalRegsFile)

EradHWBP1RegsFile 
*          1    0005e900    ********     UNINITIALIZED
                  0005e900    ********     f28004x_globalvariabledefs.obj (EradHWBP1RegsFile)

EradHWBP2RegsFile 
*          1    0005e908    ********     UNINITIALIZED
                  0005e908    ********     f28004x_globalvariabledefs.obj (EradHWBP2RegsFile)

EradHWBP3RegsFile 
*          1    0005e910    ********     UNINITIALIZED
                  0005e910    ********     f28004x_globalvariabledefs.obj (EradHWBP3RegsFile)

EradHWBP4RegsFile 
*          1    0005e918    ********     UNINITIALIZED
                  0005e918    ********     f28004x_globalvariabledefs.obj (EradHWBP4RegsFile)

EradHWBP5RegsFile 
*          1    0005e920    ********     UNINITIALIZED
                  0005e920    ********     f28004x_globalvariabledefs.obj (EradHWBP5RegsFile)

EradHWBP6RegsFile 
*          1    0005e928    ********     UNINITIALIZED
                  0005e928    ********     f28004x_globalvariabledefs.obj (EradHWBP6RegsFile)

EradHWBP7RegsFile 
*          1    0005e930    ********     UNINITIALIZED
                  0005e930    ********     f28004x_globalvariabledefs.obj (EradHWBP7RegsFile)

EradHWBP8RegsFile 
*          1    0005e938    ********     UNINITIALIZED
                  0005e938    ********     f28004x_globalvariabledefs.obj (EradHWBP8RegsFile)

EradCounter1RegsFile 
*          1    0005e980    0000000a     UNINITIALIZED
                  0005e980    0000000a     f28004x_globalvariabledefs.obj (EradCounter1RegsFile)

EradCounter2RegsFile 
*          1    0005e990    0000000a     UNINITIALIZED
                  0005e990    0000000a     f28004x_globalvariabledefs.obj (EradCounter2RegsFile)

EradCounter3RegsFile 
*          1    0005e9a0    0000000a     UNINITIALIZED
                  0005e9a0    0000000a     f28004x_globalvariabledefs.obj (EradCounter3RegsFile)

EradCounter4RegsFile 
*          1    0005e9b0    0000000a     UNINITIALIZED
                  0005e9b0    0000000a     f28004x_globalvariabledefs.obj (EradCounter4RegsFile)

DcsmBank0Z1RegsFile 
*          1    0005f000    ********     UNINITIALIZED
                  0005f000    ********     f28004x_globalvariabledefs.obj (DcsmBank0Z1RegsFile)

DcsmBank0Z2RegsFile 
*          1    0005f040    ********     UNINITIALIZED
                  0005f040    ********     f28004x_globalvariabledefs.obj (DcsmBank0Z2RegsFile)

DcsmCommonRegsFile 
*          1    0005f070    ********     UNINITIALIZED
                  0005f070    ********     f28004x_globalvariabledefs.obj (DcsmCommonRegsFile)

DcsmBank1Z1RegsFile 
*          1    0005f100    ********     UNINITIALIZED
                  0005f100    ********     f28004x_globalvariabledefs.obj (DcsmBank1Z1RegsFile)

DcsmBank1Z2RegsFile 
*          1    0005f140    ********     UNINITIALIZED
                  0005f140    ********     f28004x_globalvariabledefs.obj (DcsmBank1Z2RegsFile)

MemCfgRegsFile 
*          1    0005f400    ********     UNINITIALIZED
                  0005f400    ********     f28004x_globalvariabledefs.obj (MemCfgRegsFile)

AccessProtectionRegsFile 
*          1    0005f4c0    0000002e     UNINITIALIZED
                  0005f4c0    0000002e     f28004x_globalvariabledefs.obj (AccessProtectionRegsFile)

MemoryErrorRegsFile 
*          1    0005f500    0000003a     UNINITIALIZED
                  0005f500    0000003a     f28004x_globalvariabledefs.obj (MemoryErrorRegsFile)

Flash0CtrlRegsFile 
*          1    0005f800    ********     UNINITIALIZED
                  0005f800    ********     f28004x_globalvariabledefs.obj (Flash0CtrlRegsFile)

Flash0EccRegsFile 
*          1    0005fb00    ********     UNINITIALIZED
                  0005fb00    ********     f28004x_globalvariabledefs.obj (Flash0EccRegsFile)

PmbusaRegsFile 
*          1    ********    0000001e     UNINITIALIZED
                  ********    0000001e     f28004x_globalvariabledefs.obj (PmbusaRegsFile)

SciaRegsFile 
*          1    00007200    ********     UNINITIALIZED
                  00007200    ********     f28004x_globalvariabledefs.obj (SciaRegsFile)

ScibRegsFile 
*          1    00007210    ********     UNINITIALIZED
                  00007210    ********     f28004x_globalvariabledefs.obj (ScibRegsFile)

SyncSocRegsFile 
*          1    00007940    ********     UNINITIALIZED
                  00007940    ********     f28004x_globalvariabledefs.obj (SyncSocRegsFile)

UidRegsFile 
*          1    000703cc    ********     UNINITIALIZED
                  000703cc    ********     f28004x_globalvariabledefs.obj (UidRegsFile)

.text.1    1    0000c000    00002000     
                  0000c000    00000c15     board.obj (.text)
                  0000cc15    00000368     OLED.obj (.text)
                  0000cf7d    0000020e     device.obj (.text)
                  0000d18b    000001f2     lab_main.obj (.text:retain)
                  0000d37d    00000184     lab_main.obj (.text)
                  0000d501    000000cb     driverlib_coff.lib : dcc.obj (.text:_DCC_verifyClockFrequency)
                  0000d5cc    000000c0                        : sysctl.obj (.text:_SysCtl_setClock)
                  0000d68c    000000bd                        : sysctl.obj (.text:_SysCtl_isPLLValid)
                  0000d749    000000b8     Solar_Lib_Float.lib : PID_GRANDO_F.obj (.text)
                  0000d801    00000088     rts2800_fpu32.lib : fs_div28.asm.obj (.text)
                  0000d889    00000087     Solar_Lib_Float.lib : CNTL_2P2Z_F.obj (.text)
                  0000d910    00000073     driverlib_coff.lib : dcc.obj (.text:_DCC_setCounterSeeds)
                  0000d983    00000073                        : sysctl.obj (.text:_DCC_setCounterSeeds)
                  0000d9f6    0000005e                        : sysctl.obj (.text:_SysCtl_getClock)
                  0000da54    00000056     rts2800_fpu32.lib : boot28.asm.obj (.text)
                  0000daaa    00000052     driverlib_coff.lib : gpio.obj (.text:_GPIO_setPadConfig)
                  0000dafc    00000049                        : cmpss.obj (.text:_CMPSS_configFilterHigh)
                  0000db45    00000049                        : cmpss.obj (.text:_CMPSS_configFilterLow)
                  0000db8e    00000049                        : sysctl.obj (.text:_SysCtl_selectOscSource)
                  0000dbd7    00000048                        : cmpss.obj (.text:_CMPSS_configRamp)
                  0000dc1f    00000045                        : sysctl.obj (.text:_SysCtl_pollX1Counter)
                  0000dc64    0000003d                        : interrupt.obj (.text:_Interrupt_initModule)
                  0000dca1    0000003a                        : gpio.obj (.text:_GPIO_setAnalogMode)
                  0000dcdb    00000037                        : gpio.obj (.text:_GPIO_setControllerCore)
                  0000dd12    00000037                        : gpio.obj (.text:_GPIO_setPinConfig)
                  0000dd49    00000037                        : gpio.obj (.text:_GPIO_setQualificationMode)
                  0000dd80    00000037                        : interrupt.obj (.text:_Interrupt_enable)
                  0000ddb7    00000036                        : sysctl.obj (.text:_SysCtl_selectXTAL)
                  0000dded    00000031                        : gpio.obj (.text:_GPIO_setDirectionMode)
                  0000de1e    0000002f                        : xbar.obj (.text:_XBAR_setEPWMMuxConfig)
                  0000de4d    0000002e                        : xbar.obj (.text:_XBAR_setOutputMuxConfig)
                  0000de7b    0000002b                        : cmpss.obj (.text:_CMPSS_configLatchOnPWMSYNC)
                  0000dea6    00000029     rts2800_fpu32.lib : exit.c.obj (.text)
                  0000decf    ********     driverlib_coff.lib : adc.obj (.text:_ADC_setOffsetTrimAll)
                  0000def7    00000027                        : cmpss.obj (.text:_CMPSS_isBaseValid)
                  0000df1e    ********                        : dcc.obj (.text:_DCC_enableSingleShotMode)
                  0000df42    ********                        : sysctl.obj (.text:_DCC_enableSingleShotMode)
                  0000df66    ********     rts2800_fpu32.lib : cpy_tbl.c.obj (.text)
                  0000df8a    ********                       : i_div28.asm.obj (.text)
                  0000dfac    ********     driverlib_coff.lib : sysctl.obj (.text:_SysCtl_selectXTALSingleEnded)
                  0000dfcc    0000001e                        : dcc.obj (.text:_DCC_setCounter1ClkSource)
                  0000dfea    00000016                        : dcc.obj (.text:_DCC_disableDoneSignal)

.text.2    1    0000e847    000002e4     
                  0000e847    0000001e     driverlib_coff.lib : sysctl.obj (.text:_DCC_setCounter1ClkSource)
                  0000e865    0000001e                        : interrupt.obj (.text:_Interrupt_initVectorTable)
                  0000e883    0000001d     rts2800_fpu32.lib : memcpy.c.obj (.text)
                  0000e8a0    0000001c     driverlib_coff.lib : dcc.obj (.text:_DCC_getErrorStatus)
                  0000e8bc    0000001c                        : dcc.obj (.text:_DCC_getSingleShotStatus)
                  0000e8d8    0000001c                        : dcc.obj (.text:_DCC_setCounter0ClkSource)
                  0000e8f4    0000001c                        : sysctl.obj (.text:_DCC_setCounter0ClkSource)
                  0000e910    0000001a                        : sysctl.obj (.text:_SysCtl_getLowSpeedClock)
                  0000e92a    00000017                        : sysctl.obj (.text:_SysCtl_enablePeripheral)
                  0000e941    00000016                        : sysctl.obj (.text:_DCC_disableDoneSignal)
                  0000e957    00000016                        : dcc.obj (.text:_DCC_enableDoneSignal)
                  0000e96d    00000016                        : sysctl.obj (.text:_DCC_enableDoneSignal)
                  0000e983    00000016                        : interrupt.obj (.text:_Interrupt_defaultHandler)
                  0000e999    00000015                        : dcc.obj (.text:_DCC_clearDoneFlag)
                  0000e9ae    00000015                        : sysctl.obj (.text:_DCC_clearDoneFlag)
                  0000e9c3    00000015                        : dcc.obj (.text:_DCC_clearErrorFlag)
                  0000e9d8    00000015                        : sysctl.obj (.text:_DCC_clearErrorFlag)
                  0000e9ed    00000015                        : dcc.obj (.text:_DCC_disableErrorSignal)
                  0000ea02    00000015                        : sysctl.obj (.text:_DCC_disableErrorSignal)
                  0000ea17    00000015                        : dcc.obj (.text:_DCC_enableErrorSignal)
                  0000ea2c    00000015                        : sysctl.obj (.text:_DCC_enableErrorSignal)
                  0000ea41    00000014                        : dcc.obj (.text:_DCC_disableModule)
                  0000ea55    00000014                        : sysctl.obj (.text:_DCC_disableModule)
                  0000ea69    00000014                        : dcc.obj (.text:_DCC_enableModule)
                  0000ea7d    00000014                        : sysctl.obj (.text:_DCC_enableModule)
                  0000ea91    00000014                        : gpio.obj (.text:_GPIO_isPinValid)
                  0000eaa5    ********     rts2800_fpu32.lib : args_main.c.obj (.text)
                  0000eab7    ********     driverlib_coff.lib : dcc.obj (.text:_DCC_isBaseValid)
                  0000eac7    ********                        : sysctl.obj (.text:_DCC_isBaseValid)
                  0000ead7    0000000d                        : interrupt.obj (.text:_Interrupt_disableGlobal)
                  0000eae4    0000000d                        : interrupt.obj (.text:_Interrupt_enableGlobal)
                  0000eaf1    0000000b                        : sysctl.obj (.text:_SysCtl_isMCDClockFailureDetected)
                  0000eafc    0000000a                        : interrupt.obj (.text:_Interrupt_illegalOperationHandler)
                  0000eb06    0000000a                        : interrupt.obj (.text:_Interrupt_nmiHandler)
                  0000eb10    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  0000eb19    ********     f28004x_codestartbranch.obj (.text)
                  0000eb21    00000007     driverlib_coff.lib : sysctl.obj (.text:_SysCtl_resetMCD)
                  0000eb28    ********     rts2800_fpu32.lib : pre_init.c.obj (.text)
                  0000eb2a    00000001                       : startup.c.obj (.text)

MODULE SUMMARY

       Module                           code   initialized data   uninitialized data
       ------                           ----   ----------------   ------------------
    .\
       OLED.obj                         872    2146               2072              
       lab_main.obj                     886    72                 168               
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           1758   2218               2240              
                                                                                    
    .\device\
       device.obj                       526    99                 4                 
       f28004x_codestartbranch.obj      10     0                  0                 
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           536    99                 4                 
                                                                                    
    .\headers\source\
       f28004x_globalvariabledefs.obj   0      0                  9873              
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           0      0                  9873              
                                                                                    
    .\syscfg\
       board.obj                        3093   414                0                 
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           3093   414                0                 
                                                                                    
    C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/lib/Solar_Lib_Float.lib
       PID_GRANDO_F.obj                 184    0                  0                 
       CNTL_2P2Z_F.obj                  135    0                  0                 
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           319    0                  0                 
                                                                                    
    C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\driverlib_coff.lib
       sysctl.obj                       1167   154                0                 
       dcc.obj                          652    151                0                 
       gpio.obj                         374    152                0                 
       cmpss.obj                        300    77                 0                 
       interrupt.obj                    214    81                 0                 
       adc.obj                          40     75                 0                 
       xbar.obj                         93     0                  0                 
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           2840   690                0                 
                                                                                    
    C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\rts2800_fpu32.lib
       fs_div28.asm.obj                 136    0                  0                 
       boot28.asm.obj                   86     0                  0                 
       exit.c.obj                       41     14                 6                 
       cpy_tbl.c.obj                    36     0                  0                 
       i_div28.asm.obj                  34     0                  0                 
       memcpy.c.obj                     29     0                  0                 
       _lock.c.obj                      9      10                 4                 
       args_main.c.obj                  18     0                  0                 
       pre_init.c.obj                   2      0                  0                 
       startup.c.obj                    1      0                  0                 
    +--+--------------------------------+------+------------------+--------------------+
       Total:                           392    24                 10                
                                                                                    
       Stack:                           0      0                  256               
    +--+--------------------------------+------+------------------+--------------------+
       Grand Total:                     8938   3445               12383             


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000400      10 (00000400)     __stack

00000b00      2c (00000b00)     _AdcaResultRegs
00000b20      2c (00000b00)     _AdcbResultRegs

00000b40      2d (00000b40)     _AdccResultRegs

00000c00      30 (00000c00)     _CpuTimer0Regs
00000c08      30 (00000c00)     _CpuTimer1Regs
00000c10      30 (00000c00)     _CpuTimer2Regs

00000ce0      33 (00000cc0)     _PieCtrlRegs

00000d00      34 (00000d00)     _PieVectTable

********      40 (********)     _DmaRegs

00001400      50 (00001400)     _Cla1Regs

00003000      c0 (00003000)     _Clb1LogicCfgRegs

00003100      c4 (00003100)     _Clb1LogicCtrlRegs

00003200      c8 (00003200)     _Clb1DataExchRegs

00003400      d0 (00003400)     _Clb2LogicCfgRegs

00003500      d4 (00003500)     _Clb2LogicCtrlRegs

00003600      d8 (00003600)     _Clb2DataExchRegs

00003800      e0 (00003800)     _Clb3LogicCfgRegs

00003900      e4 (00003900)     _Clb3LogicCtrlRegs

00003a00      e8 (00003a00)     _Clb3DataExchRegs

00003c00      f0 (00003c00)     _Clb4LogicCfgRegs

00003d00      f4 (00003d00)     _Clb4LogicCtrlRegs

00003e00      f8 (00003e00)     _Clb4DataExchRegs

00004000     100 (00004000)     _EPwm1Regs

00004100     104 (00004100)     _EPwm2Regs

00004200     108 (00004200)     _EPwm3Regs

00004300     10c (00004300)     _EPwm4Regs

00004400     110 (00004400)     _EPwm5Regs

00004500     114 (00004500)     _EPwm6Regs

00004600     118 (00004600)     _EPwm7Regs

00004700     11c (00004700)     _EPwm8Regs

00005100     144 (00005100)     _EQep1Regs

00005140     145 (00005140)     _EQep2Regs

********     148 (********)     _ECap1Regs

********     149 (********)     _ECap2Regs

********     14a (********)     _ECap3Regs

000052c0     14b (000052c0)     _ECap4Regs

********     14c (********)     _ECap5Regs

********     14d (********)     _ECap6Regs
00005360     14d (********)     _HRCap6Regs

********     14e (********)     _ECap7Regs
000053a0     14e (********)     _HRCap7Regs

00005b00     16c (00005b00)     _Pga1Regs
00005b10     16c (00005b00)     _Pga2Regs
00005b20     16c (00005b00)     _Pga3Regs
00005b30     16c (00005b00)     _Pga4Regs

00005b40     16d (00005b40)     _Pga5Regs
00005b50     16d (00005b40)     _Pga6Regs
00005b60     16d (00005b40)     _Pga7Regs

00005c00     170 (00005c00)     _DacaRegs
00005c10     170 (00005c00)     _DacbRegs

00005c80     172 (00005c80)     _Cmpss1Regs
00005ca0     172 (00005c80)     _Cmpss2Regs

00005cc0     173 (00005cc0)     _Cmpss3Regs
00005ce0     173 (00005cc0)     _Cmpss4Regs

00005d00     174 (00005d00)     _Cmpss5Regs
00005d20     174 (00005d00)     _Cmpss6Regs

00005d40     175 (00005d40)     _Cmpss7Regs

00005e00     178 (00005e00)     _Sdfm1Regs

00006100     184 (00006100)     _SpiaRegs
00006110     184 (00006100)     _SpibRegs

000061c0     187 (000061c0)     _ClaPromCrc0Regs

********     190 (********)     _PmbusaRegs

00006600     198 (00006600)     _FsiTxaRegs

00006680     19a (00006680)     _FsiRxaRegs

00006a00     1a8 (00006a00)     _LinaRegs

00007000     1c0 (00007000)     _WdRegs

00007060     1c1 (00007040)     _NmiIntruptRegs
00007070     1c1 (00007040)     _XintRegs

00007200     1c8 (00007200)     _SciaRegs
00007210     1c8 (00007200)     _ScibRegs

00007300     1cc (00007300)     _I2caRegs

00007400     1d0 (00007400)     _AdcaRegs

00007480     1d2 (00007480)     _AdcbRegs

00007500     1d4 (00007500)     _AdccRegs

00007900     1e4 (00007900)     _InputXbarRegs
00007920     1e4 (00007900)     _XbarRegs

00007940     1e5 (00007940)     _SyncSocRegs

********     1e6 (********)     _DmaClaSrcSelRegs

00007a00     1e8 (00007a00)     _EPwmXbarRegs

00007a40     1e9 (00007a40)     _CLBXbarRegs

00007a80     1ea (00007a80)     _OutputXbarRegs

00007c00     1f0 (00007c00)     _GpioCtrlRegs

00007f00     1fc (00007f00)     _GpioDataRegs

0000eb2c     3ac (0000eb00)     ___TI_enable_exit_profile_output
0000eb2e     3ac (0000eb00)     ___TI_cleanup_ptr
0000eb30     3ac (0000eb00)     ___TI_dtors_ptr
0000eb32     3ac (0000eb00)     _Example_PassCount
0000eb34     3ac (0000eb00)     _Example_Fail
0000eb36     3ac (0000eb00)     __lock
0000eb38     3ac (0000eb00)     __unlock

0000f380     3ce (0000f380)     _direction
0000f382     3ce (0000f380)     _oldCount
0000f384     3ce (0000f380)     _newCount
0000f386     3ce (0000f380)     _currentEncoderPosition
0000f388     3ce (0000f380)     _Count
0000f38a     3ce (0000f380)     _Probe1
0000f398     3ce (0000f380)     _cntl_2p2z_coeffs_PR
0000f3a8     3ce (0000f380)     _cntl_2p2z_vars_PR

0000f3c0     3cf (0000f3c0)     _Variable1
0000f3d6     3cf (0000f3c0)     _pid_grando_controller_V

0000f400     3d0 (0000f400)     _pid_grando_controller_I

00048000    1200 (00048000)     _CanaRegs

0004a000    1280 (0004a000)     _CanbRegs

0005d000    1740 (0005d000)     _DevCfgRegs

0005d200    1748 (0005d200)     _ClkCfgRegs

0005d300    174c (0005d300)     _CpuSysRegs

0005d500    1754 (0005d500)     _SysPeriphAcRegs

0005d700    175c (0005d700)     _AnalogSubsysRegs

0005e700    179c (0005e700)     _Dcc0Regs

0005e800    17a0 (0005e800)     _EradGlobalRegs

0005e900    17a4 (0005e900)     _EradHWBP1Regs
0005e908    17a4 (0005e900)     _EradHWBP2Regs
0005e910    17a4 (0005e900)     _EradHWBP3Regs
0005e918    17a4 (0005e900)     _EradHWBP4Regs
0005e920    17a4 (0005e900)     _EradHWBP5Regs
0005e928    17a4 (0005e900)     _EradHWBP6Regs
0005e930    17a4 (0005e900)     _EradHWBP7Regs
0005e938    17a4 (0005e900)     _EradHWBP8Regs

0005e980    17a6 (0005e980)     _EradCounter1Regs
0005e990    17a6 (0005e980)     _EradCounter2Regs
0005e9a0    17a6 (0005e980)     _EradCounter3Regs
0005e9b0    17a6 (0005e980)     _EradCounter4Regs

0005f000    17c0 (0005f000)     _DcsmBank0Z1Regs

0005f040    17c1 (0005f040)     _DcsmBank0Z2Regs
0005f070    17c1 (0005f040)     _DcsmCommonRegs

0005f100    17c4 (0005f100)     _DcsmBank1Z1Regs

0005f140    17c5 (0005f140)     _DcsmBank1Z2Regs

0005f400    17d0 (0005f400)     _MemCfgRegs

0005f4c0    17d3 (0005f4c0)     _AccessProtectionRegs

0005f500    17d4 (0005f500)     _MemoryErrorRegs

0005f800    17e0 (0005f800)     _Flash0CtrlRegs

0005fb00    17ec (0005fb00)     _Flash0EccRegs

000703cc    1c0f (000703c0)     _UidRegs


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                              
----  -------   ----                              
abs   ffffffff  .text                             
1     0000dea6  C$$EXIT                           
1     0000d801  FS$$DIV                           
1     0000df8a  I$$DIV                            
1     0000df9b  I$$MOD                            
1     0000c81e  _ADC_init                         
1     0000decf  _ADC_setOffsetTrimAll             
1     0000cb5e  _AKEY1_init                       
1     0000cb72  _AKEY2_init                       
1     0000cb86  _AKEY3_init                       
1     0000cb9a  _AKEY4_init                       
1     0000c898  _ASYSCTL_init                     
1     0005f4c0  _AccessProtectionRegs             
1     00007400  _AdcaRegs                         
1     00000b00  _AdcaResultRegs                   
1     00007480  _AdcbRegs                         
1     00000b20  _AdcbResultRegs                   
1     00007500  _AdccRegs                         
1     00000b40  _AdccResultRegs                   
1     0005d700  _AnalogSubsysRegs                 
1     0000c7a5  _Board_init                       
1     00007a40  _CLBXbarRegs                      
1     0000dafc  _CMPSS_configFilterHigh           
1     0000db45  _CMPSS_configFilterLow            
1     0000de7b  _CMPSS_configLatchOnPWMSYNC       
1     0000dbd7  _CMPSS_configRamp                 
1     0000c89e  _CMPSS_init                       
1     0000d8f3  _CNTL_2P2Z_F_COEFFS_init          
1     0000d89f  _CNTL_2P2Z_F_FUNC                 
1     0000d889  _CNTL_2P2Z_F_VARS_init            
1     00048000  _CanaRegs                         
1     0004a000  _CanbRegs                         
1     00001400  _Cla1Regs                         
1     000061c0  _ClaPromCrc0Regs                  
1     00003200  _Clb1DataExchRegs                 
1     00003000  _Clb1LogicCfgRegs                 
1     00003100  _Clb1LogicCtrlRegs                
1     00003600  _Clb2DataExchRegs                 
1     00003400  _Clb2LogicCfgRegs                 
1     00003500  _Clb2LogicCtrlRegs                
1     00003a00  _Clb3DataExchRegs                 
1     00003800  _Clb3LogicCfgRegs                 
1     00003900  _Clb3LogicCtrlRegs                
1     00003e00  _Clb4DataExchRegs                 
1     00003c00  _Clb4LogicCfgRegs                 
1     00003d00  _Clb4LogicCtrlRegs                
1     0005d200  _ClkCfgRegs                       
1     00005c80  _Cmpss1Regs                       
1     00005ca0  _Cmpss2Regs                       
1     00005cc0  _Cmpss3Regs                       
1     00005ce0  _Cmpss4Regs                       
1     00005d00  _Cmpss5Regs                       
1     00005d20  _Cmpss6Regs                       
1     00005d40  _Cmpss7Regs                       
1     0000f388  _Count                            
1     0005d300  _CpuSysRegs                       
1     00000c00  _CpuTimer0Regs                    
1     00000c08  _CpuTimer1Regs                    
1     00000c10  _CpuTimer2Regs                    
1     0000c90f  _DAC_init                         
1     0000d501  _DCC_verifyClockFrequency         
1     00005c00  _DacaRegs                         
1     00005c10  _DacbRegs                         
1     0005e700  _Dcc0Regs                         
1     0005f000  _DcsmBank0Z1Regs                  
1     0005f040  _DcsmBank0Z2Regs                  
1     0005f100  _DcsmBank1Z1Regs                  
1     0005f140  _DcsmBank1Z2Regs                  
1     0005f070  _DcsmCommonRegs                   
1     0000cd44  _Delay_1ms                        
1     0000cd54  _Delay_3us                        
1     0000cd34  _Delay_50ms                       
1     0005d000  _DevCfgRegs                       
1     0000d061  _Device_enableAllPeripherals      
1     0000d012  _Device_init                      
1     0000d141  _Device_initGPIO                  
1     0000d151  _Device_verifyXTAL                
1     ********  _DmaClaSrcSelRegs                 
1     ********  _DmaRegs                          
1     ********  _ECap1Regs                        
1     ********  _ECap2Regs                        
1     ********  _ECap3Regs                        
1     000052c0  _ECap4Regs                        
1     ********  _ECap5Regs                        
1     ********  _ECap6Regs                        
1     ********  _ECap7Regs                        
1     0000cb1b  _EPWMXBAR_init                    
1     0000c94e  _EPWM_init                        
1     00004000  _EPwm1Regs                        
1     00004100  _EPwm2Regs                        
1     00004200  _EPwm3Regs                        
1     00004300  _EPwm4Regs                        
1     00004400  _EPwm5Regs                        
1     00004500  _EPwm6Regs                        
1     00004600  _EPwm7Regs                        
1     00004700  _EPwm8Regs                        
1     00007a00  _EPwmXbarRegs                     
1     00005100  _EQep1Regs                        
1     00005140  _EQep2Regs                        
1     0005e980  _EradCounter1Regs                 
1     0005e990  _EradCounter2Regs                 
1     0005e9a0  _EradCounter3Regs                 
1     0005e9b0  _EradCounter4Regs                 
1     0005e800  _EradGlobalRegs                   
1     0005e900  _EradHWBP1Regs                    
1     0005e908  _EradHWBP2Regs                    
1     0005e910  _EradHWBP3Regs                    
1     0005e918  _EradHWBP4Regs                    
1     0005e920  _EradHWBP5Regs                    
1     0005e928  _EradHWBP6Regs                    
1     0005e930  _EradHWBP7Regs                    
1     0005e938  _EradHWBP8Regs                    
1     0000eb34  _Example_Fail                     
1     0000eb32  _Example_PassCount                
1     0005f800  _Flash0CtrlRegs                   
1     0005fb00  _Flash0EccRegs                    
1     00006680  _FsiRxaRegs                       
1     00006600  _FsiTxaRegs                       
1     0000cb4a  _GPIO32I2CA_SDA_init              
1     0000cb36  _GPIO33I2CA_SCL_init              
1     0000cb27  _GPIO_init                        
1     0000dca1  _GPIO_setAnalogMode               
1     0000dcdb  _GPIO_setControllerCore           
1     0000dded  _GPIO_setDirectionMode            
1     0000daaa  _GPIO_setPadConfig                
1     0000dd12  _GPIO_setPinConfig                
1     0000dd49  _GPIO_setQualificationMode        
1     00007c00  _GpioCtrlRegs                     
1     00007f00  _GpioDataRegs                     
1     00005360  _HRCap6Regs                       
1     000053a0  _HRCap7Regs                       
1     00007300  _I2caRegs                         
1     0000cc50  _IIC_Start                        
1     0000cc6b  _IIC_Stop                         
1     0000cc86  _IIC_Wait_Ack                     
1     0000cbc2  _INPUTXBAR_init                   
1     0000cbca  _INTERRUPT_init                   
1     0000d18b  _INT_myADCA_1_ISR                 
1     0000d370  _INT_myEPWM1_TZ_ISR               
1     00007900  _InputXbarRegs                    
1     0000e983  _Interrupt_defaultHandler         
1     0000dd80  _Interrupt_enable                 
1     0000eafc  _Interrupt_illegalOperationHandler
1     0000dc64  _Interrupt_initModule             
1     0000e865  _Interrupt_initVectorTable        
1     0000eb06  _Interrupt_nmiHandler             
1     00006a00  _LinaRegs                         
1     0005f400  _MemCfgRegs                       
1     0005f500  _MemoryErrorRegs                  
1     00007060  _NmiIntruptRegs                   
1     0000cd8f  _OLED_Clear                       
1     0000cd82  _OLED_Display_Off                 
1     0000cd75  _OLED_Display_On                  
1     0000cede  _OLED_DrawBMP                     
1     0000cf0c  _OLED_Init                        
1     0000cdb3  _OLED_On                          
1     0000cd5f  _OLED_Set_Pos                     
1     0000cdd7  _OLED_ShowChar                    
1     0000ce45  _OLED_ShowNum                     
1     0000cec2  _OLED_ShowString                  
1     0000cd01  _OLED_WR_Byte                     
1     0000cbe3  _OUTPUTXBAR_init                  
1     00007a80  _OutputXbarRegs                   
1     0000d791  _PID_GRANDO_F_FUNC                
1     0000d749  _PID_GRANDO_F_init                
1     00005b00  _Pga1Regs                         
1     00005b10  _Pga2Regs                         
1     00005b20  _Pga3Regs                         
1     00005b30  _Pga4Regs                         
1     00005b40  _Pga5Regs                         
1     00005b50  _Pga6Regs                         
1     00005b60  _Pga7Regs                         
1     00000ce0  _PieCtrlRegs                      
1     00000d00  _PieVectTable                     
1     0000c7c0  _PinMux_init                      
1     ********  _PmbusaRegs                       
1     0000f38a  _Probe1                           
1     0000cbf7  _SYNC_init                        
1     00007200  _SciaRegs                         
1     00007210  _ScibRegs                         
1     00005e00  _Sdfm1Regs                        
1     0000d4e0  _SetDuty                          
1     00006100  _SpiaRegs                         
1     00006110  _SpibRegs                         
1     00007940  _SyncSocRegs                      
0     000000f6  _SysCtl_delay                     
1     0000d9f6  _SysCtl_getClock                  
1     0000e910  _SysCtl_getLowSpeedClock          
1     0000d68c  _SysCtl_isPLLValid                
1     0000db8e  _SysCtl_selectOscSource           
1     0000ddb7  _SysCtl_selectXTAL                
1     0000dfac  _SysCtl_selectXTALSingleEnded     
1     0000d5cc  _SysCtl_setClock                  
1     0005d500  _SysPeriphAcRegs                  
1     000703cc  _UidRegs                          
1     0000f3c0  _Variable1                        
1     00007000  _WdRegs                           
1     0000cc9c  _Write_IIC_Byte                   
1     0000ccd3  _Write_IIC_Command                
1     0000ccea  _Write_IIC_Data                   
1     0000de1e  _XBAR_setEPWMMuxConfig            
1     0000de4d  _XBAR_setOutputMuxConfig          
1     00007920  _XbarRegs                         
1     00007070  _XintRegs                         
1     00000500  __STACK_END                       
abs   ********  __STACK_SIZE                      
1     0000eb2e  ___TI_cleanup_ptr                 
1     0000eb30  ___TI_dtors_ptr                   
1     0000eb2c  ___TI_enable_exit_profile_output  
abs   ffffffff  ___TI_pprof_out_hndl              
abs   ffffffff  ___TI_prof_data_size              
abs   ffffffff  ___TI_prof_data_start             
abs   ffffffff  ___binit__                        
abs   ffffffff  ___c_args__                       
1     0000e000  ___cinit__                        
1     0000d184  ___error__                        
abs   ffffffff  ___etext__                        
abs   ffffffff  ___pinit__                        
abs   ffffffff  ___text__                         
1     0000eaa5  __args_main                       
1     0000eb36  __lock                            
1     0000eb18  __nop                             
1     0000eb14  __register_lock                   
1     0000eb10  __register_unlock                 
1     00000400  __stack                           
1     0000eb2a  __system_post_cinit               
1     0000eb28  __system_pre_init                 
1     0000eb38  __unlock                          
1     0000dea6  _abort                            
1     0000da54  _c_int00                          
1     0000f398  _cntl_2p2z_coeffs_PR              
1     0000f3a8  _cntl_2p2z_vars_PR                
1     0000df66  _copy_in                          
1     0000f386  _currentEncoderPosition           
1     0000f380  _direction                        
1     0000dea8  _exit                             
1     0000cd0f  _fill_picture                     
1     0000d3dd  _main                             
1     0000e883  _memcpy                           
1     0000c821  _myADCA_init                      
1     0000cbae  _myBoardLED0_GPIO_init            
1     0000c8a1  _myCMPSS0_init                    
1     0000c914  _myDACA_init                      
1     0000c931  _myDACB_init                      
1     0000cb1e  _myEPWMXBAR0_init                 
1     0000cbc5  _myINPUTXBARINPUT0_init           
1     0000cbe6  _myOUTPUTXBAR0_init               
1     0000f384  _newCount                         
1     0000f382  _oldCount                         
1     0000ce33  _oled_pow                         
1     0000f400  _pid_grando_controller_I          
1     0000f3d6  _pid_grando_controller_V          
abs   ffffffff  binit                             
1     0000e000  cinit                             
0     ********  code_start                        
abs   ffffffff  etext                             
abs   ffffffff  pinit                             


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                              
----  -------   ----                              
0     ********  code_start                        
0     000000f6  _SysCtl_delay                     
1     00000400  __stack                           
1     00000500  __STACK_END                       
1     00000b00  _AdcaResultRegs                   
1     00000b20  _AdcbResultRegs                   
1     00000b40  _AdccResultRegs                   
1     00000c00  _CpuTimer0Regs                    
1     00000c08  _CpuTimer1Regs                    
1     00000c10  _CpuTimer2Regs                    
1     00000ce0  _PieCtrlRegs                      
1     00000d00  _PieVectTable                     
1     ********  _DmaRegs                          
1     00001400  _Cla1Regs                         
1     00003000  _Clb1LogicCfgRegs                 
1     00003100  _Clb1LogicCtrlRegs                
1     00003200  _Clb1DataExchRegs                 
1     00003400  _Clb2LogicCfgRegs                 
1     00003500  _Clb2LogicCtrlRegs                
1     00003600  _Clb2DataExchRegs                 
1     00003800  _Clb3LogicCfgRegs                 
1     00003900  _Clb3LogicCtrlRegs                
1     00003a00  _Clb3DataExchRegs                 
1     00003c00  _Clb4LogicCfgRegs                 
1     00003d00  _Clb4LogicCtrlRegs                
1     00003e00  _Clb4DataExchRegs                 
1     00004000  _EPwm1Regs                        
1     00004100  _EPwm2Regs                        
1     00004200  _EPwm3Regs                        
1     00004300  _EPwm4Regs                        
1     00004400  _EPwm5Regs                        
1     00004500  _EPwm6Regs                        
1     00004600  _EPwm7Regs                        
1     00004700  _EPwm8Regs                        
1     00005100  _EQep1Regs                        
1     00005140  _EQep2Regs                        
1     ********  _ECap1Regs                        
1     ********  _ECap2Regs                        
1     ********  _ECap3Regs                        
1     000052c0  _ECap4Regs                        
1     ********  _ECap5Regs                        
1     ********  _ECap6Regs                        
1     00005360  _HRCap6Regs                       
1     ********  _ECap7Regs                        
1     000053a0  _HRCap7Regs                       
1     00005b00  _Pga1Regs                         
1     00005b10  _Pga2Regs                         
1     00005b20  _Pga3Regs                         
1     00005b30  _Pga4Regs                         
1     00005b40  _Pga5Regs                         
1     00005b50  _Pga6Regs                         
1     00005b60  _Pga7Regs                         
1     00005c00  _DacaRegs                         
1     00005c10  _DacbRegs                         
1     00005c80  _Cmpss1Regs                       
1     00005ca0  _Cmpss2Regs                       
1     00005cc0  _Cmpss3Regs                       
1     00005ce0  _Cmpss4Regs                       
1     00005d00  _Cmpss5Regs                       
1     00005d20  _Cmpss6Regs                       
1     00005d40  _Cmpss7Regs                       
1     00005e00  _Sdfm1Regs                        
1     00006100  _SpiaRegs                         
1     00006110  _SpibRegs                         
1     000061c0  _ClaPromCrc0Regs                  
1     ********  _PmbusaRegs                       
1     00006600  _FsiTxaRegs                       
1     00006680  _FsiRxaRegs                       
1     00006a00  _LinaRegs                         
1     00007000  _WdRegs                           
1     00007060  _NmiIntruptRegs                   
1     00007070  _XintRegs                         
1     00007200  _SciaRegs                         
1     00007210  _ScibRegs                         
1     00007300  _I2caRegs                         
1     00007400  _AdcaRegs                         
1     00007480  _AdcbRegs                         
1     00007500  _AdccRegs                         
1     00007900  _InputXbarRegs                    
1     00007920  _XbarRegs                         
1     00007940  _SyncSocRegs                      
1     ********  _DmaClaSrcSelRegs                 
1     00007a00  _EPwmXbarRegs                     
1     00007a40  _CLBXbarRegs                      
1     00007a80  _OutputXbarRegs                   
1     00007c00  _GpioCtrlRegs                     
1     00007f00  _GpioDataRegs                     
1     0000c7a5  _Board_init                       
1     0000c7c0  _PinMux_init                      
1     0000c81e  _ADC_init                         
1     0000c821  _myADCA_init                      
1     0000c898  _ASYSCTL_init                     
1     0000c89e  _CMPSS_init                       
1     0000c8a1  _myCMPSS0_init                    
1     0000c90f  _DAC_init                         
1     0000c914  _myDACA_init                      
1     0000c931  _myDACB_init                      
1     0000c94e  _EPWM_init                        
1     0000cb1b  _EPWMXBAR_init                    
1     0000cb1e  _myEPWMXBAR0_init                 
1     0000cb27  _GPIO_init                        
1     0000cb36  _GPIO33I2CA_SCL_init              
1     0000cb4a  _GPIO32I2CA_SDA_init              
1     0000cb5e  _AKEY1_init                       
1     0000cb72  _AKEY2_init                       
1     0000cb86  _AKEY3_init                       
1     0000cb9a  _AKEY4_init                       
1     0000cbae  _myBoardLED0_GPIO_init            
1     0000cbc2  _INPUTXBAR_init                   
1     0000cbc5  _myINPUTXBARINPUT0_init           
1     0000cbca  _INTERRUPT_init                   
1     0000cbe3  _OUTPUTXBAR_init                  
1     0000cbe6  _myOUTPUTXBAR0_init               
1     0000cbf7  _SYNC_init                        
1     0000cc50  _IIC_Start                        
1     0000cc6b  _IIC_Stop                         
1     0000cc86  _IIC_Wait_Ack                     
1     0000cc9c  _Write_IIC_Byte                   
1     0000ccd3  _Write_IIC_Command                
1     0000ccea  _Write_IIC_Data                   
1     0000cd01  _OLED_WR_Byte                     
1     0000cd0f  _fill_picture                     
1     0000cd34  _Delay_50ms                       
1     0000cd44  _Delay_1ms                        
1     0000cd54  _Delay_3us                        
1     0000cd5f  _OLED_Set_Pos                     
1     0000cd75  _OLED_Display_On                  
1     0000cd82  _OLED_Display_Off                 
1     0000cd8f  _OLED_Clear                       
1     0000cdb3  _OLED_On                          
1     0000cdd7  _OLED_ShowChar                    
1     0000ce33  _oled_pow                         
1     0000ce45  _OLED_ShowNum                     
1     0000cec2  _OLED_ShowString                  
1     0000cede  _OLED_DrawBMP                     
1     0000cf0c  _OLED_Init                        
1     0000d012  _Device_init                      
1     0000d061  _Device_enableAllPeripherals      
1     0000d141  _Device_initGPIO                  
1     0000d151  _Device_verifyXTAL                
1     0000d184  ___error__                        
1     0000d18b  _INT_myADCA_1_ISR                 
1     0000d370  _INT_myEPWM1_TZ_ISR               
1     0000d3dd  _main                             
1     0000d4e0  _SetDuty                          
1     0000d501  _DCC_verifyClockFrequency         
1     0000d5cc  _SysCtl_setClock                  
1     0000d68c  _SysCtl_isPLLValid                
1     0000d749  _PID_GRANDO_F_init                
1     0000d791  _PID_GRANDO_F_FUNC                
1     0000d801  FS$$DIV                           
1     0000d889  _CNTL_2P2Z_F_VARS_init            
1     0000d89f  _CNTL_2P2Z_F_FUNC                 
1     0000d8f3  _CNTL_2P2Z_F_COEFFS_init          
1     0000d9f6  _SysCtl_getClock                  
1     0000da54  _c_int00                          
1     0000daaa  _GPIO_setPadConfig                
1     0000dafc  _CMPSS_configFilterHigh           
1     0000db45  _CMPSS_configFilterLow            
1     0000db8e  _SysCtl_selectOscSource           
1     0000dbd7  _CMPSS_configRamp                 
1     0000dc64  _Interrupt_initModule             
1     0000dca1  _GPIO_setAnalogMode               
1     0000dcdb  _GPIO_setControllerCore           
1     0000dd12  _GPIO_setPinConfig                
1     0000dd49  _GPIO_setQualificationMode        
1     0000dd80  _Interrupt_enable                 
1     0000ddb7  _SysCtl_selectXTAL                
1     0000dded  _GPIO_setDirectionMode            
1     0000de1e  _XBAR_setEPWMMuxConfig            
1     0000de4d  _XBAR_setOutputMuxConfig          
1     0000de7b  _CMPSS_configLatchOnPWMSYNC       
1     0000dea6  C$$EXIT                           
1     0000dea6  _abort                            
1     0000dea8  _exit                             
1     0000decf  _ADC_setOffsetTrimAll             
1     0000df66  _copy_in                          
1     0000df8a  I$$DIV                            
1     0000df9b  I$$MOD                            
1     0000dfac  _SysCtl_selectXTALSingleEnded     
1     0000e000  ___cinit__                        
1     0000e000  cinit                             
1     0000e865  _Interrupt_initVectorTable        
1     0000e883  _memcpy                           
1     0000e910  _SysCtl_getLowSpeedClock          
1     0000e983  _Interrupt_defaultHandler         
1     0000eaa5  __args_main                       
1     0000eafc  _Interrupt_illegalOperationHandler
1     0000eb06  _Interrupt_nmiHandler             
1     0000eb10  __register_unlock                 
1     0000eb14  __register_lock                   
1     0000eb18  __nop                             
1     0000eb28  __system_pre_init                 
1     0000eb2a  __system_post_cinit               
1     0000eb2c  ___TI_enable_exit_profile_output  
1     0000eb2e  ___TI_cleanup_ptr                 
1     0000eb30  ___TI_dtors_ptr                   
1     0000eb32  _Example_PassCount                
1     0000eb34  _Example_Fail                     
1     0000eb36  __lock                            
1     0000eb38  __unlock                          
1     0000f380  _direction                        
1     0000f382  _oldCount                         
1     0000f384  _newCount                         
1     0000f386  _currentEncoderPosition           
1     0000f388  _Count                            
1     0000f38a  _Probe1                           
1     0000f398  _cntl_2p2z_coeffs_PR              
1     0000f3a8  _cntl_2p2z_vars_PR                
1     0000f3c0  _Variable1                        
1     0000f3d6  _pid_grando_controller_V          
1     0000f400  _pid_grando_controller_I          
1     00048000  _CanaRegs                         
1     0004a000  _CanbRegs                         
1     0005d000  _DevCfgRegs                       
1     0005d200  _ClkCfgRegs                       
1     0005d300  _CpuSysRegs                       
1     0005d500  _SysPeriphAcRegs                  
1     0005d700  _AnalogSubsysRegs                 
1     0005e700  _Dcc0Regs                         
1     0005e800  _EradGlobalRegs                   
1     0005e900  _EradHWBP1Regs                    
1     0005e908  _EradHWBP2Regs                    
1     0005e910  _EradHWBP3Regs                    
1     0005e918  _EradHWBP4Regs                    
1     0005e920  _EradHWBP5Regs                    
1     0005e928  _EradHWBP6Regs                    
1     0005e930  _EradHWBP7Regs                    
1     0005e938  _EradHWBP8Regs                    
1     0005e980  _EradCounter1Regs                 
1     0005e990  _EradCounter2Regs                 
1     0005e9a0  _EradCounter3Regs                 
1     0005e9b0  _EradCounter4Regs                 
1     0005f000  _DcsmBank0Z1Regs                  
1     0005f040  _DcsmBank0Z2Regs                  
1     0005f070  _DcsmCommonRegs                   
1     0005f100  _DcsmBank1Z1Regs                  
1     0005f140  _DcsmBank1Z2Regs                  
1     0005f400  _MemCfgRegs                       
1     0005f4c0  _AccessProtectionRegs             
1     0005f500  _MemoryErrorRegs                  
1     0005f800  _Flash0CtrlRegs                   
1     0005fb00  _Flash0EccRegs                    
1     000703cc  _UidRegs                          
abs   ********  __STACK_SIZE                      
abs   ffffffff  .text                             
abs   ffffffff  ___TI_pprof_out_hndl              
abs   ffffffff  ___TI_prof_data_size              
abs   ffffffff  ___TI_prof_data_start             
abs   ffffffff  ___binit__                        
abs   ffffffff  ___c_args__                       
abs   ffffffff  ___etext__                        
abs   ffffffff  ___pinit__                        
abs   ffffffff  ___text__                         
abs   ffffffff  binit                             
abs   ffffffff  etext                             
abs   ffffffff  pinit                             

[257 symbols]
