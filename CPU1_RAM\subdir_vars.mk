################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
CMD_SRCS += \
../28004x_generic_ram_lnk.cmd 

SYSCFG_SRCS += \
../lab_adc_launchpad.syscfg 

LIB_SRCS += \
O:/Ti/C20005.01/C2000Ware_5_01_00_00/driverlib/f28004x/driverlib/ccs/Debug/driverlib.lib 

C_SRCS += \
../OLED.c \
../VSG.c \
./syscfg/board.c \
./syscfg/c2000ware_libraries.c \
../lab_main.c 

GEN_FILES += \
./syscfg/board.c \
./syscfg/board.opt \
./syscfg/c2000ware_libraries.opt \
./syscfg/c2000ware_libraries.c 

GEN_MISC_DIRS += \
./syscfg 

C_DEPS += \
./OLED.d \
./VSG.d \
./syscfg/board.d \
./syscfg/c2000ware_libraries.d \
./lab_main.d 

GEN_OPTS += \
./syscfg/board.opt \
./syscfg/c2000ware_libraries.opt 

OBJS += \
./OLED.obj \
./VSG.obj \
./syscfg/board.obj \
./syscfg/c2000ware_libraries.obj \
./lab_main.obj 

GEN_MISC_FILES += \
./syscfg/board.h \
./syscfg/board.cmd.genlibs \
./syscfg/pinmux.csv \
./syscfg/epwm.dot \
./syscfg/adc.dot \
./syscfg/c2000ware_libraries.cmd.genlibs \
./syscfg/c2000ware_libraries.h \
./syscfg/clocktree.h 

GEN_MISC_DIRS__QUOTED += \
"syscfg" 

OBJS__QUOTED += \
"OLED.obj" \
"VSG.obj" \
"syscfg\board.obj" \
"syscfg\c2000ware_libraries.obj" \
"lab_main.obj" 

GEN_MISC_FILES__QUOTED += \
"syscfg\board.h" \
"syscfg\board.cmd.genlibs" \
"syscfg\pinmux.csv" \
"syscfg\epwm.dot" \
"syscfg\adc.dot" \
"syscfg\c2000ware_libraries.cmd.genlibs" \
"syscfg\c2000ware_libraries.h" \
"syscfg\clocktree.h" 

C_DEPS__QUOTED += \
"OLED.d" \
"VSG.d" \
"syscfg\board.d" \
"syscfg\c2000ware_libraries.d" \
"lab_main.d" 

GEN_FILES__QUOTED += \
"syscfg\board.c" \
"syscfg\board.opt" \
"syscfg\c2000ware_libraries.opt" \
"syscfg\c2000ware_libraries.c" 

C_SRCS__QUOTED += \
"../OLED.c" \
"../VSG.c" \
"./syscfg/board.c" \
"./syscfg/c2000ware_libraries.c" \
"../lab_main.c" 

SYSCFG_SRCS__QUOTED += \
"../lab_adc_launchpad.syscfg" 


