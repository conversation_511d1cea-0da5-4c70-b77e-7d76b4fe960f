<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>F280049_three_phase_inverting</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>driverlib.lib</name>
			<type>1</type>
			<locationURI>COM_TI_C2000WARE_SOFTWARE_PACKAGE_INSTALL_DIR/driverlib/f28004x/driverlib/ccs/Debug/driverlib.lib</locationURI>
		</link>
	</linkedResources>
	<variableList>
		<variable>
			<name>C2000WARE_DLIB_ROOT</name>
			<value>$%7BCOM_TI_C2000WARE_SOFTWARE_PACKAGE_INSTALL_DIR%7D/driverlib/f28004x/driverlib</value>
		</variable>
		<variable>
			<name>C2000WARE_ROOT</name>
			<value>$%7BCOM_TI_C2000WARE_SOFTWARE_PACKAGE_INSTALL_DIR%7D</value>
		</variable>
		<variable>
			<name>CONTROL</name>
			<value>$%7BTI_PRODUCTS_DIR__TIREX%7D/controlSUITE/libs/app_libs/solar/v1.2/float</value>
		</variable>
	</variableList>
</projectDescription>
