################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
device/%.obj: ../device/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: C2000 Compiler'
	"C:/ti/ccs1271/ccs/tools/compiler/ti-cgt-c2000_22.6.1.LTS/bin/cl2000" -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --isr_save_vcu_regs=on --tmu_support=tmu0 --vcu_support=vcu0 -Ooff --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/headers/include" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/common/include" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/device" --include_path="O:/Ti/C20005.01/C2000Ware_5_01_00_00/driverlib/f28004x/driverlib" --include_path="C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/include" --include_path="C:/ti/ccs1271/ccs/tools/compiler/ti-cgt-c2000_22.6.1.LTS/include" --preinclude="C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/include/Solar_F.h" --advice:performance=all --define=DEBUG --define=CPU1 --define=USE_ADC_REFERENCE_INTERNAL --define=_LAUNCHXL_F280049C --diag_suppress=10063 --diag_warning=225 --diag_wrap=off --display_error_number --abi=coffabi --preproc_with_compile --preproc_dependency="device/$(basename $(<F)).d_raw" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/CPU1_RAM/syscfg" --obj_directory="device" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

device/%.obj: ../device/%.asm $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: C2000 Compiler'
	"C:/ti/ccs1271/ccs/tools/compiler/ti-cgt-c2000_22.6.1.LTS/bin/cl2000" -v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --isr_save_vcu_regs=on --tmu_support=tmu0 --vcu_support=vcu0 -Ooff --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/headers/include" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/common/include" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/device" --include_path="O:/Ti/C20005.01/C2000Ware_5_01_00_00/driverlib/f28004x/driverlib" --include_path="C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/include" --include_path="C:/ti/ccs1271/ccs/tools/compiler/ti-cgt-c2000_22.6.1.LTS/include" --preinclude="C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/include/Solar_F.h" --advice:performance=all --define=DEBUG --define=CPU1 --define=USE_ADC_REFERENCE_INTERNAL --define=_LAUNCHXL_F280049C --diag_suppress=10063 --diag_warning=225 --diag_wrap=off --display_error_number --abi=coffabi --preproc_with_compile --preproc_dependency="device/$(basename $(<F)).d_raw" --include_path="C:/workplacedsp280049/F280049_three_phase_inverting/CPU1_RAM/syscfg" --obj_directory="device" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


