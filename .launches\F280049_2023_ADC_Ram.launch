<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="com.ti.ccstudio.debug.launchType.device.debugging">
    <stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_DEBUGGER_PROPERTIES.TMS320F280049C_LaunchPad.ccxml.Texas Instruments XDS110 USB Debug Probe_0/C28xx_CPU1" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot; ?&gt;&#10;&lt;PropertyValues&gt;&#10;&#10;  &lt;property id=&quot;ConnectOnStartup&quot;&gt;&#10;    &lt;curValue&gt;1&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;  &lt;property id=&quot;EnableInstalledBreakpoint&quot;&gt;&#10;    &lt;curValue&gt;1&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;  &lt;property id=&quot;IgnoreSoftLaunchFailures&quot;&gt;&#10;    &lt;curValue&gt;0&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;&lt;/PropertyValues&gt;&#10;"/>
    <stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_PROGRAM.TMS320F280049C_LaunchPad.ccxml.Texas Instruments XDS110 USB Debug Probe_0/C28xx_CPU1" value="${build_artifact:F280049_2023_ADC_Ram}"/>
    <stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_PROJECT.TMS320F280049C_LaunchPad.ccxml.Texas Instruments XDS110 USB Debug Probe_0/C28xx_CPU1" value="F280049_2023_ADC_Ram"/>
    <stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_TARGET_CONFIG" value="${target_config_active_default:F280049_2023_ADC_Ram}"/>
    <stringAttribute key="com.ti.ccstudio.debug.debugModel.MRU_PROGRAM.TMS320F280049C_LaunchPad.ccxml.Texas Instruments XDS110 USB Debug Probe_0/C28xx_CPU1" value="${build_artifact:F280049_2023_ADC_Ram}"/>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/F280049_2023_ADC_Ram"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="4"/>
    </listAttribute>
    <stringAttribute key="org.eclipse.debug.core.source_locator_id" value="com.ti.ccstudio.debug.sourceLocator"/>
    <stringAttribute key="org.eclipse.debug.core.source_locator_memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#13;&#10;&lt;sourceLookupDirector&gt;&#13;&#10;    &lt;sourceContainers duplicates=&quot;false&quot;&gt;&#13;&#10;        &lt;container memento=&quot;&amp;lt;?xml version=&amp;quot;1.0&amp;quot; encoding=&amp;quot;UTF-8&amp;quot; standalone=&amp;quot;no&amp;quot;?&amp;gt;&amp;#13;&amp;#10;&amp;lt;default/&amp;gt;&amp;#13;&amp;#10;&quot; typeId=&quot;org.eclipse.debug.core.containerType.default&quot;/&gt;&#13;&#10;        &lt;container memento=&quot;&amp;lt;?xml version=&amp;quot;1.0&amp;quot; encoding=&amp;quot;UTF-8&amp;quot; standalone=&amp;quot;no&amp;quot;?&amp;gt;&amp;#13;&amp;#10;&amp;lt;cpuSpecificContainer cpuName=&amp;quot;Texas Instruments XDS110 USB Debug Probe_0/C28xx_CPU1&amp;quot;&amp;gt;&amp;#13;&amp;#10;    &amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;project name=&amp;amp;quot;F280049_2023_ADC_Ram&amp;amp;quot; referencedProjects=&amp;amp;quot;true&amp;amp;quot;/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;org.eclipse.debug.core.containerType.project&amp;quot;/&amp;gt;&amp;#13;&amp;#10;    &amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;default/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;org.eclipse.debug.core.containerType.default&amp;quot;/&amp;gt;&amp;#13;&amp;#10;    &amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;productsSource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.products.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;    &amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;deviceLibrarySource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.device.library.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;    &amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;librarySource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.library.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;/cpuSpecificContainer&amp;gt;&amp;#13;&amp;#10;&quot; typeId=&quot;com.ti.ccstudio.debug.containerType.cpu.specific&quot;/&gt;&#13;&#10;    &lt;/sourceContainers&gt;&#13;&#10;&lt;/sourceLookupDirector&gt;&#13;&#10;"/>
    <stringAttribute key="org.eclipse.dsf.launch.MEMORY_BLOCKS" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&lt;memoryBlockExpressionList context=&quot;reserved-for-future-use&quot;/&gt;"/>
</launchConfiguration>
