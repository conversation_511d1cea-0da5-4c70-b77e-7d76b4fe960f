/*
 * Copyright (c) 2020 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef BOARD_H
#define BOARD_H

//*****************************************************************************
//
// If building with a C++ compiler, make all of the definitions in this header
// have a C binding.
//
//*****************************************************************************
#ifdef __cplusplus
extern "C"
{
#endif

//
// Included Files
//

#include "driverlib.h"
#include "device.h"

//*****************************************************************************
//
// PinMux Configurations
//
//*****************************************************************************

//
// EPWM1 -> myEPWM1 Pinmux
//
//
// EPWM1_A - GPIO Settings
//
#define GPIO_PIN_EPWM1_A 0
#define myEPWM1_EPWMA_GPIO 0
#define myEPWM1_EPWMA_PIN_CONFIG GPIO_0_EPWM1_A
//
// EPWM1_B - GPIO Settings
//
#define GPIO_PIN_EPWM1_B 1
#define myEPWM1_EPWMB_GPIO 1
#define myEPWM1_EPWMB_PIN_CONFIG GPIO_1_EPWM1_B

//
// EPWM2 -> myEPWM2 Pinmux
//
//
// EPWM2_A - GPIO Settings
//
#define GPIO_PIN_EPWM2_A 2
#define myEPWM2_EPWMA_GPIO 2
#define myEPWM2_EPWMA_PIN_CONFIG GPIO_2_EPWM2_A
//
// EPWM2_B - GPIO Settings
//
#define GPIO_PIN_EPWM2_B 3
#define myEPWM2_EPWMB_GPIO 3
#define myEPWM2_EPWMB_PIN_CONFIG GPIO_3_EPWM2_B

//
// EPWM3 -> myEPWM3 Pinmux
//
//
// EPWM3_A - GPIO Settings
//
#define GPIO_PIN_EPWM3_A 4
#define myEPWM3_EPWMA_GPIO 4
#define myEPWM3_EPWMA_PIN_CONFIG GPIO_4_EPWM3_A
//
// EPWM3_B - GPIO Settings
//
#define GPIO_PIN_EPWM3_B 5
#define myEPWM3_EPWMB_GPIO 5
#define myEPWM3_EPWMB_PIN_CONFIG GPIO_5_EPWM3_B
//
// GPIO30 - GPIO Settings
//
#define GPIO33I2CA_SCL_GPIO_PIN_CONFIG GPIO_30_GPIO30
//
// GPIO32 - GPIO Settings
//
#define GPIO32I2CA_SDA_GPIO_PIN_CONFIG GPIO_32_GPIO32
//
// GPIO17 - GPIO Settings
//
#define AKEY1_GPIO_PIN_CONFIG GPIO_17_GPIO17
//
// GPIO16 - GPIO Settings
//
#define AKEY2_GPIO_PIN_CONFIG GPIO_16_GPIO16
//
// GPIO56 - GPIO Settings
//
#define AKEY3_GPIO_PIN_CONFIG GPIO_56_GPIO56
//
// GPIO57 - GPIO Settings
//
#define AKEY4_GPIO_PIN_CONFIG GPIO_57_GPIO57
//
// GPIO34 - GPIO Settings
//
#define myBoardLED0_GPIO_GPIO_PIN_CONFIG GPIO_34_GPIO34

//
// OUTPUTXBAR1 -> myOUTPUTXBAR0 Pinmux
//
//
// OUTPUTXBAR1 - GPIO Settings
//
#define GPIO_PIN_OUTPUTXBAR1 58
#define myOUTPUTXBAR0_OUTPUTXBAR_GPIO 58
#define myOUTPUTXBAR0_OUTPUTXBAR_PIN_CONFIG GPIO_58_OUTPUTXBAR1

//*****************************************************************************
//
// ADC Configurations
//
//*****************************************************************************
#define myADCA_BASE ADCA_BASE
#define myADCA_RESULT_BASE ADCARESULT_BASE
#define myADCA_SOC0 ADC_SOC_NUMBER0
#define myADCA_FORCE_SOC0 ADC_FORCE_SOC0
#define myADCA_SAMPLE_WINDOW_SOC0 200
#define myADCA_TRIGGER_SOURCE_SOC0 ADC_TRIGGER_EPWM1_SOCA
#define myADCA_CHANNEL_SOC0 ADC_CH_ADCIN4
#define myADCA_SOC1 ADC_SOC_NUMBER1
#define myADCA_FORCE_SOC1 ADC_FORCE_SOC1
#define myADCA_SAMPLE_WINDOW_SOC1 200
#define myADCA_TRIGGER_SOURCE_SOC1 ADC_TRIGGER_EPWM1_SOCA
#define myADCA_CHANNEL_SOC1 ADC_CH_ADCIN5
#define myADCA_SOC2 ADC_SOC_NUMBER2
#define myADCA_FORCE_SOC2 ADC_FORCE_SOC2
#define myADCA_SAMPLE_WINDOW_SOC2 200
#define myADCA_TRIGGER_SOURCE_SOC2 ADC_TRIGGER_EPWM1_SOCA
#define myADCA_CHANNEL_SOC2 ADC_CH_ADCIN6
#define myADCA_SOC3 ADC_SOC_NUMBER3
#define myADCA_FORCE_SOC3 ADC_FORCE_SOC3
#define myADCA_SAMPLE_WINDOW_SOC3 200
#define myADCA_TRIGGER_SOURCE_SOC3 ADC_TRIGGER_EPWM1_SOCA
#define myADCA_CHANNEL_SOC3 ADC_CH_ADCIN8
void myADCA_init();

#define myADCB_BASE ADCB_BASE
#define myADCB_RESULT_BASE ADCBRESULT_BASE
#define myADCB_SOC0 ADC_SOC_NUMBER0
#define myADCB_FORCE_SOC0 ADC_FORCE_SOC0
#define myADCB_SAMPLE_WINDOW_SOC0 200
#define myADCB_TRIGGER_SOURCE_SOC0 ADC_TRIGGER_EPWM1_SOCA
#define myADCB_CHANNEL_SOC0 ADC_CH_ADCIN0
#define myADCB_SOC1 ADC_SOC_NUMBER1
#define myADCB_FORCE_SOC1 ADC_FORCE_SOC1
#define myADCB_SAMPLE_WINDOW_SOC1 200
#define myADCB_TRIGGER_SOURCE_SOC1 ADC_TRIGGER_EPWM1_SOCA
#define myADCB_CHANNEL_SOC1 ADC_CH_ADCIN1
#define myADCB_SOC2 ADC_SOC_NUMBER2
#define myADCB_FORCE_SOC2 ADC_FORCE_SOC2
#define myADCB_SAMPLE_WINDOW_SOC2 200
#define myADCB_TRIGGER_SOURCE_SOC2 ADC_TRIGGER_EPWM1_SOCA
#define myADCB_CHANNEL_SOC2 ADC_CH_ADCIN2
#define myADCB_SOC3 ADC_SOC_NUMBER3
#define myADCB_FORCE_SOC3 ADC_FORCE_SOC3
#define myADCB_SAMPLE_WINDOW_SOC3 200
#define myADCB_TRIGGER_SOURCE_SOC3 ADC_TRIGGER_EPWM1_SOCA
#define myADCB_CHANNEL_SOC3 ADC_CH_ADCIN3
void myADCB_init();

#define myADCC_BASE ADCC_BASE
#define myADCC_RESULT_BASE ADCCRESULT_BASE
#define myADCC_SOC0 ADC_SOC_NUMBER0
#define myADCC_FORCE_SOC0 ADC_FORCE_SOC0
#define myADCC_SAMPLE_WINDOW_SOC0 200
#define myADCC_TRIGGER_SOURCE_SOC0 ADC_TRIGGER_EPWM1_SOCA
#define myADCC_CHANNEL_SOC0 ADC_CH_ADCIN4
#define myADCC_SOC1 ADC_SOC_NUMBER1
#define myADCC_FORCE_SOC1 ADC_FORCE_SOC1
#define myADCC_SAMPLE_WINDOW_SOC1 200
#define myADCC_TRIGGER_SOURCE_SOC1 ADC_TRIGGER_EPWM1_SOCA
#define myADCC_CHANNEL_SOC1 ADC_CH_ADCIN5
#define myADCC_SOC2 ADC_SOC_NUMBER2
#define myADCC_FORCE_SOC2 ADC_FORCE_SOC2
#define myADCC_SAMPLE_WINDOW_SOC2 200
#define myADCC_TRIGGER_SOURCE_SOC2 ADC_TRIGGER_EPWM1_SOCA
#define myADCC_CHANNEL_SOC2 ADC_CH_ADCIN8
#define myADCC_SOC3 ADC_SOC_NUMBER3
#define myADCC_FORCE_SOC3 ADC_FORCE_SOC3
#define myADCC_SAMPLE_WINDOW_SOC3 200
#define myADCC_TRIGGER_SOURCE_SOC3 ADC_TRIGGER_EPWM1_SOCA
#define myADCC_CHANNEL_SOC3 ADC_CH_ADCIN14
#define myADCC_SOC4 ADC_SOC_NUMBER4
#define myADCC_FORCE_SOC4 ADC_FORCE_SOC4
#define myADCC_SAMPLE_WINDOW_SOC4 200
#define myADCC_TRIGGER_SOURCE_SOC4 ADC_TRIGGER_SW_ONLY
#define myADCC_CHANNEL_SOC4 ADC_CH_ADCIN2
#define myADCC_SOC5 ADC_SOC_NUMBER5
#define myADCC_FORCE_SOC5 ADC_FORCE_SOC5
#define myADCC_SAMPLE_WINDOW_SOC5 200
#define myADCC_TRIGGER_SOURCE_SOC5 ADC_TRIGGER_SW_ONLY
#define myADCC_CHANNEL_SOC5 ADC_CH_ADCIN0
void myADCC_init();


//*****************************************************************************
//
// ASYSCTL Configurations
//
//*****************************************************************************

//*****************************************************************************
//
// CMPSS Configurations
//
//*****************************************************************************
#define myCMPSS0_BASE CMPSS1_BASE
#define myCMPSS0_HIGH_COMP_BASE CMPSS1_BASE    
#define myCMPSS0_LOW_COMP_BASE CMPSS1_BASE    
void myCMPSS0_init();

//*****************************************************************************
//
// DAC Configurations
//
//*****************************************************************************
#define myDACA_BASE DACA_BASE
void myDACA_init();
#define myDACB_BASE DACB_BASE
void myDACB_init();

//*****************************************************************************
//
// EPWM Configurations
//
//*****************************************************************************
#define myEPWM1_BASE EPWM1_BASE
#define myEPWM1_TBPRD 2500
#define myEPWM1_COUNTER_MODE EPWM_COUNTER_MODE_UP_DOWN
#define myEPWM1_TBPHS 0
#define myEPWM1_CMPA 1250
#define myEPWM1_CMPB 1250
#define myEPWM1_CMPC 0
#define myEPWM1_CMPD 0
#define myEPWM1_DBRED 0
#define myEPWM1_DBFED 0
#define myEPWM1_TZA_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM1_TZB_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM1_OSHT_SOURCES EPWM_TZ_SIGNAL_DCAEVT1
#define myEPWM1_TZ_INTERRUPT_SOURCES EPWM_TZ_INTERRUPT_DCAEVT1
#define myEPWM1_INTERRUPT_SOURCE EPWM_INT_TBCTR_DISABLED
#define myEPWM2_BASE EPWM2_BASE
#define myEPWM2_TBPRD 2500
#define myEPWM2_COUNTER_MODE EPWM_COUNTER_MODE_UP_DOWN
#define myEPWM2_TBPHS 0
#define myEPWM2_CMPA 1250
#define myEPWM2_CMPB 1250
#define myEPWM2_CMPC 0
#define myEPWM2_CMPD 0
#define myEPWM2_DBRED 0
#define myEPWM2_DBFED 0
#define myEPWM2_TZA_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM2_TZB_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM2_OSHT_SOURCES EPWM_TZ_SIGNAL_DCAEVT1
#define myEPWM2_TZ_INTERRUPT_SOURCES EPWM_TZ_INTERRUPT_DCAEVT1
#define myEPWM2_INTERRUPT_SOURCE EPWM_INT_TBCTR_DISABLED
#define myEPWM3_BASE EPWM3_BASE
#define myEPWM3_TBPRD 2500
#define myEPWM3_COUNTER_MODE EPWM_COUNTER_MODE_UP_DOWN
#define myEPWM3_TBPHS 0
#define myEPWM3_CMPA 1250
#define myEPWM3_CMPB 1250
#define myEPWM3_CMPC 0
#define myEPWM3_CMPD 0
#define myEPWM3_DBRED 0
#define myEPWM3_DBFED 0
#define myEPWM3_TZA_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM3_TZB_ACTION EPWM_TZ_ACTION_HIGH_Z
#define myEPWM3_OSHT_SOURCES EPWM_TZ_SIGNAL_DCAEVT1
#define myEPWM3_TZ_INTERRUPT_SOURCES EPWM_TZ_INTERRUPT_DCAEVT1
#define myEPWM3_INTERRUPT_SOURCE EPWM_INT_TBCTR_DISABLED

//*****************************************************************************
//
// EPWMXBAR Configurations
//
//*****************************************************************************
void myEPWMXBAR0_init();
#define myEPWMXBAR0 XBAR_TRIP4
#define myEPWMXBAR0_ENABLED_MUXES (XBAR_MUX00)

//*****************************************************************************
//
// GPIO Configurations
//
//*****************************************************************************
#define GPIO33I2CA_SCL 30
void GPIO33I2CA_SCL_init();
#define GPIO32I2CA_SDA 32
void GPIO32I2CA_SDA_init();
#define AKEY1 17
void AKEY1_init();
#define AKEY2 16
void AKEY2_init();
#define AKEY3 56
void AKEY3_init();
#define AKEY4 57
void AKEY4_init();
#define myBoardLED0_GPIO 34
void myBoardLED0_GPIO_init();

//*****************************************************************************
//
// INPUTXBAR Configurations
//
//*****************************************************************************
#define myINPUTXBARINPUT0_SOURCE 1
#define myINPUTXBARINPUT0_INPUT XBAR_INPUT2
void myINPUTXBARINPUT0_init();

//*****************************************************************************
//
// INTERRUPT Configurations
//
//*****************************************************************************

// Interrupt Settings for INT_myADCA_1
#define INT_myADCA_1 INT_ADCA1
#define INT_myADCA_1_INTERRUPT_ACK_GROUP INTERRUPT_ACK_GROUP1
extern __interrupt void INT_myADCA_1_ISR(void);

// Interrupt Settings for INT_myEPWM1_TZ
#define INT_myEPWM1_TZ INT_EPWM1_TZ
#define INT_myEPWM1_TZ_INTERRUPT_ACK_GROUP INTERRUPT_ACK_GROUP2
extern __interrupt void INT_myEPWM1_TZ_ISR(void);

//*****************************************************************************
//
// OUTPUTXBAR Configurations
//
//*****************************************************************************
void myOUTPUTXBAR0_init();
#define myOUTPUTXBAR0 XBAR_OUTPUT1
#define myOUTPUTXBAR0_ENABLED_MUXES (XBAR_MUX00)

//*****************************************************************************
//
// SYNC Scheme Configurations
//
//*****************************************************************************

//*****************************************************************************
//
// Board Configurations
//
//*****************************************************************************
void	Board_init();
void	ADC_init();
void	ASYSCTL_init();
void	CMPSS_init();
void	DAC_init();
void	EPWM_init();
void	EPWMXBAR_init();
void	GPIO_init();
void	INPUTXBAR_init();
void	INTERRUPT_init();
void	OUTPUTXBAR_init();
void	SYNC_init();
void	PinMux_init();

//*****************************************************************************
//
// Mark the end of the C bindings section for C++ compilers.
//
//*****************************************************************************
#ifdef __cplusplus
}
#endif

#endif  // end of BOARD_H definition
