<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v22.6.0.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x6788d0d0</link_time>
   <link_errors>0x0</link_errors>
   <output_file>F280049_adc.out</output_file>
   <entry_point>
      <name>code_start</name>
      <address>0x0</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\syscfg\</path>
         <kind>object</kind>
         <file>board.obj</file>
         <name>board.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\syscfg\</path>
         <kind>object</kind>
         <file>c2000ware_libraries.obj</file>
         <name>c2000ware_libraries.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\</path>
         <kind>object</kind>
         <file>lab_main.obj</file>
         <name>lab_main.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\device\</path>
         <kind>object</kind>
         <file>device.obj</file>
         <name>device.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\device\</path>
         <kind>object</kind>
         <file>f28004x_codestartbranch.obj</file>
         <name>f28004x_codestartbranch.obj</name>
      </input_file>
      <input_file id="fl-c">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>adc.obj</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>dcc.obj</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>gpio.obj</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>interrupt.obj</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\</path>
         <kind>archive</kind>
         <file>driverlib_coff.lib</file>
         <name>sysctl.obj</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\controlSUITE\libs\app_libs\solar\v1.2\float\lib\</path>
         <kind>archive</kind>
         <file>Solar_Lib_Float.lib</file>
         <name>CNTL_2P2Z_F.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fs_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-11">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.TI.ramfunc</name>
         <load_address>0x127</load_address>
         <run_address>0x127</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x747</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text</name>
         <load_address>0x8747</load_address>
         <run_address>0x8747</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.text:_GPIO_setDirectionMode</name>
         <load_address>0x87cf</load_address>
         <run_address>0x87cf</run_address>
         <size>0x31</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text</name>
         <load_address>0x8800</load_address>
         <run_address>0x8800</run_address>
         <size>0x210</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text</name>
         <load_address>0x8a10</load_address>
         <run_address>0x8a10</run_address>
         <size>0x1f2</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:retain</name>
         <load_address>0x8c02</load_address>
         <run_address>0x8c02</run_address>
         <size>0xdf</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text:_DCC_verifyClockFrequency</name>
         <load_address>0x8ce1</load_address>
         <run_address>0x8ce1</run_address>
         <size>0xcb</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text:_SysCtl_setClock</name>
         <load_address>0x8dac</load_address>
         <run_address>0x8dac</run_address>
         <size>0xc0</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text:_SysCtl_isPLLValid</name>
         <load_address>0x8e6c</load_address>
         <run_address>0x8e6c</run_address>
         <size>0xbd</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text</name>
         <load_address>0x8f29</load_address>
         <run_address>0x8f29</run_address>
         <size>0x87</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text:_SysCtl_selectOscSource</name>
         <load_address>0x8fb0</load_address>
         <run_address>0x8fb0</run_address>
         <size>0x49</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text:_SysCtl_resetMCD</name>
         <load_address>0x8ff9</load_address>
         <run_address>0x8ff9</run_address>
         <size>0x7</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text:_DCC_setCounterSeeds</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0x73</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text:_DCC_setCounterSeeds</name>
         <load_address>0x9073</load_address>
         <run_address>0x9073</run_address>
         <size>0x73</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text:_SysCtl_getClock</name>
         <load_address>0x90e6</load_address>
         <run_address>0x90e6</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.text</name>
         <load_address>0x9144</load_address>
         <run_address>0x9144</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text:_GPIO_setPadConfig</name>
         <load_address>0x919a</load_address>
         <run_address>0x919a</run_address>
         <size>0x52</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text:_SysCtl_pollX1Counter</name>
         <load_address>0x91ec</load_address>
         <run_address>0x91ec</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text:_Interrupt_initModule</name>
         <load_address>0x9231</load_address>
         <run_address>0x9231</run_address>
         <size>0x3d</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text:_GPIO_setAnalogMode</name>
         <load_address>0x926e</load_address>
         <run_address>0x926e</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text:_GPIO_setControllerCore</name>
         <load_address>0x92a8</load_address>
         <run_address>0x92a8</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text:_GPIO_setPinConfig</name>
         <load_address>0x92df</load_address>
         <run_address>0x92df</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text:_GPIO_setQualificationMode</name>
         <load_address>0x9316</load_address>
         <run_address>0x9316</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text:_Interrupt_enable</name>
         <load_address>0x934d</load_address>
         <run_address>0x934d</run_address>
         <size>0x37</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text:_SysCtl_selectXTAL</name>
         <load_address>0x9384</load_address>
         <run_address>0x9384</run_address>
         <size>0x36</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-91">
         <name>.text</name>
         <load_address>0x93ba</load_address>
         <run_address>0x93ba</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text:_ADC_setOffsetTrimAll</name>
         <load_address>0x93e3</load_address>
         <run_address>0x93e3</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text:_DCC_enableSingleShotMode</name>
         <load_address>0x940b</load_address>
         <run_address>0x940b</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text:_DCC_enableSingleShotMode</name>
         <load_address>0x942f</load_address>
         <run_address>0x942f</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text</name>
         <load_address>0x9453</load_address>
         <run_address>0x9453</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text:_SysCtl_selectXTALSingleEnded</name>
         <load_address>0x9477</load_address>
         <run_address>0x9477</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text:_DCC_setCounter1ClkSource</name>
         <load_address>0x9497</load_address>
         <run_address>0x9497</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text:_DCC_setCounter1ClkSource</name>
         <load_address>0x94b5</load_address>
         <run_address>0x94b5</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text:_Interrupt_initVectorTable</name>
         <load_address>0x94d3</load_address>
         <run_address>0x94d3</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text</name>
         <load_address>0x94f1</load_address>
         <run_address>0x94f1</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text:_DCC_getErrorStatus</name>
         <load_address>0x950e</load_address>
         <run_address>0x950e</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text:_DCC_getSingleShotStatus</name>
         <load_address>0x952a</load_address>
         <run_address>0x952a</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text:_DCC_setCounter0ClkSource</name>
         <load_address>0x9546</load_address>
         <run_address>0x9546</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text:_DCC_setCounter0ClkSource</name>
         <load_address>0x9562</load_address>
         <run_address>0x9562</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text:_SysCtl_getLowSpeedClock</name>
         <load_address>0x957e</load_address>
         <run_address>0x957e</run_address>
         <size>0x1a</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text:_SysCtl_enablePeripheral</name>
         <load_address>0x9598</load_address>
         <run_address>0x9598</run_address>
         <size>0x17</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text:_DCC_disableDoneSignal</name>
         <load_address>0x95af</load_address>
         <run_address>0x95af</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text:_DCC_disableDoneSignal</name>
         <load_address>0x95c5</load_address>
         <run_address>0x95c5</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text:_DCC_enableDoneSignal</name>
         <load_address>0x95db</load_address>
         <run_address>0x95db</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text:_DCC_enableDoneSignal</name>
         <load_address>0x95f1</load_address>
         <run_address>0x95f1</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:_Interrupt_defaultHandler</name>
         <load_address>0x9607</load_address>
         <run_address>0x9607</run_address>
         <size>0x16</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text:_DCC_clearDoneFlag</name>
         <load_address>0x961d</load_address>
         <run_address>0x961d</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text:_DCC_clearDoneFlag</name>
         <load_address>0x9632</load_address>
         <run_address>0x9632</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text:_DCC_clearErrorFlag</name>
         <load_address>0x9647</load_address>
         <run_address>0x9647</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text:_DCC_clearErrorFlag</name>
         <load_address>0x965c</load_address>
         <run_address>0x965c</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text:_DCC_disableErrorSignal</name>
         <load_address>0x9671</load_address>
         <run_address>0x9671</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text:_DCC_disableErrorSignal</name>
         <load_address>0x9686</load_address>
         <run_address>0x9686</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text:_DCC_enableErrorSignal</name>
         <load_address>0x969b</load_address>
         <run_address>0x969b</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text:_DCC_enableErrorSignal</name>
         <load_address>0x96b0</load_address>
         <run_address>0x96b0</run_address>
         <size>0x15</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text:_DCC_disableModule</name>
         <load_address>0x96c5</load_address>
         <run_address>0x96c5</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text:_DCC_disableModule</name>
         <load_address>0x96d9</load_address>
         <run_address>0x96d9</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text:_DCC_enableModule</name>
         <load_address>0x96ed</load_address>
         <run_address>0x96ed</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text:_DCC_enableModule</name>
         <load_address>0x9701</load_address>
         <run_address>0x9701</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text:_GPIO_isPinValid</name>
         <load_address>0x9715</load_address>
         <run_address>0x9715</run_address>
         <size>0x14</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text</name>
         <load_address>0x9729</load_address>
         <run_address>0x9729</run_address>
         <size>0x12</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text:_DCC_isBaseValid</name>
         <load_address>0x973b</load_address>
         <run_address>0x973b</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text:_DCC_isBaseValid</name>
         <load_address>0x974b</load_address>
         <run_address>0x974b</run_address>
         <size>0x10</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text:_Interrupt_disableGlobal</name>
         <load_address>0x975b</load_address>
         <run_address>0x975b</run_address>
         <size>0xd</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text:_Interrupt_enableGlobal</name>
         <load_address>0x9768</load_address>
         <run_address>0x9768</run_address>
         <size>0xd</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.text:_SysCtl_isMCDClockFailureDetected</name>
         <load_address>0x9775</load_address>
         <run_address>0x9775</run_address>
         <size>0xb</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-47">
         <name>.text:_Interrupt_illegalOperationHandler</name>
         <load_address>0x9780</load_address>
         <run_address>0x9780</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text:_Interrupt_nmiHandler</name>
         <load_address>0x978a</load_address>
         <run_address>0x978a</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text</name>
         <load_address>0x9794</load_address>
         <run_address>0x9794</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-2e">
         <name>.text</name>
         <load_address>0x979d</load_address>
         <run_address>0x979d</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text</name>
         <load_address>0x97a5</load_address>
         <run_address>0x97a5</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text</name>
         <load_address>0x97a7</load_address>
         <run_address>0x97a7</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.cinit</name>
         <load_address>0xf6</load_address>
         <run_address>0xf6</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.cinit</name>
         <load_address>0x104</load_address>
         <run_address>0x104</run_address>
         <size>0xd</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-27">
         <name>.cinit</name>
         <load_address>0x111</load_address>
         <run_address>0x111</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-98">
         <name>.cinit:__lock</name>
         <load_address>0x11b</load_address>
         <run_address>0x11b</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.cinit:__unlock</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-1f">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xad40</run_address>
         <size>0x32</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.ebss:_AdcBuf</name>
         <uninitialized>true</uninitialized>
         <run_address>0xad00</run_address>
         <size>0x32</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-28">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xacec</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0xace6</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-97">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xacf0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0xacf2</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-13">
         <name>.econst:.string</name>
         <load_address>0xa800</load_address>
         <run_address>0xa800</run_address>
         <size>0x158</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-21">
         <name>.econst:.string</name>
         <load_address>0xa958</load_address>
         <run_address>0xa958</run_address>
         <size>0xcc</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-50">
         <name>.econst:.string</name>
         <load_address>0xaa24</load_address>
         <run_address>0xaa24</run_address>
         <size>0x9a</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-40">
         <name>.econst:.string</name>
         <load_address>0xaabe</load_address>
         <run_address>0xaabe</run_address>
         <size>0x98</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.econst:.string</name>
         <load_address>0xab56</load_address>
         <run_address>0xab56</run_address>
         <size>0x97</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-29">
         <name>.econst:.string</name>
         <load_address>0xabee</load_address>
         <run_address>0xabee</run_address>
         <size>0x59</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-49">
         <name>.econst:.string</name>
         <load_address>0xac48</load_address>
         <run_address>0xac48</run_address>
         <size>0x51</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-34">
         <name>.econst:.string</name>
         <load_address>0xac9a</load_address>
         <run_address>0xac9a</run_address>
         <size>0x4b</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-12">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6918</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18">
         <name>.debug_info</name>
         <load_address>0x6918</load_address>
         <run_address>0x6918</run_address>
         <size>0x333</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x6c4b</load_address>
         <run_address>0x6c4b</run_address>
         <size>0x15fa</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-26">
         <name>.debug_info</name>
         <load_address>0x8245</load_address>
         <run_address>0x8245</run_address>
         <size>0x248a</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_info</name>
         <load_address>0xa6cf</load_address>
         <run_address>0xa6cf</run_address>
         <size>0x12d</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-33">
         <name>.debug_info</name>
         <load_address>0xa7fc</load_address>
         <run_address>0xa7fc</run_address>
         <size>0x6a9</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_info</name>
         <load_address>0xaea5</load_address>
         <run_address>0xaea5</run_address>
         <size>0x1cf9</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_info</name>
         <load_address>0xcb9e</load_address>
         <run_address>0xcb9e</run_address>
         <size>0x1113</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0xdcb1</load_address>
         <run_address>0xdcb1</run_address>
         <size>0xcb5</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0xe966</load_address>
         <run_address>0xe966</run_address>
         <size>0x2e83</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_info</name>
         <load_address>0x117e9</load_address>
         <run_address>0x117e9</run_address>
         <size>0x6b5</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0x11e9e</load_address>
         <run_address>0x11e9e</run_address>
         <size>0x173</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0x12011</load_address>
         <run_address>0x12011</run_address>
         <size>0x114</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_info</name>
         <load_address>0x12125</load_address>
         <run_address>0x12125</run_address>
         <size>0x561</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_info</name>
         <load_address>0x12686</load_address>
         <run_address>0x12686</run_address>
         <size>0x43a</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x12ac0</load_address>
         <run_address>0x12ac0</run_address>
         <size>0x3ad</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_info</name>
         <load_address>0x12e6d</load_address>
         <run_address>0x12e6d</run_address>
         <size>0x3aa</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x13217</load_address>
         <run_address>0x13217</run_address>
         <size>0x544</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0x1375b</load_address>
         <run_address>0x1375b</run_address>
         <size>0x4cc</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x13c27</load_address>
         <run_address>0x13c27</run_address>
         <size>0x467</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0x1408e</load_address>
         <run_address>0x1408e</run_address>
         <size>0x9c</size>
      </object_component>
      <object_component id="oc-14">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x678</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_frame</name>
         <load_address>0x678</load_address>
         <run_address>0x678</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0x1d8</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2a">
         <name>.debug_frame</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x194</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_frame</name>
         <load_address>0xa0c</load_address>
         <run_address>0xa0c</run_address>
         <size>0x44</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0xa50</load_address>
         <run_address>0xa50</run_address>
         <size>0x1f4</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_frame</name>
         <load_address>0xc44</load_address>
         <run_address>0xc44</run_address>
         <size>0xec</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_frame</name>
         <load_address>0xd30</load_address>
         <run_address>0xd30</run_address>
         <size>0x10c</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_frame</name>
         <load_address>0xe3c</load_address>
         <run_address>0xe3c</run_address>
         <size>0x2c0</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_frame</name>
         <load_address>0x132c</load_address>
         <run_address>0x132c</run_address>
         <size>0xd6</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_frame</name>
         <load_address>0x10fc</load_address>
         <run_address>0x10fc</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x115c</load_address>
         <run_address>0x115c</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_frame</name>
         <load_address>0x119c</load_address>
         <run_address>0x119c</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_frame</name>
         <load_address>0x11dc</load_address>
         <run_address>0x11dc</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_frame</name>
         <load_address>0x121c</load_address>
         <run_address>0x121c</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_frame</name>
         <load_address>0x127c</load_address>
         <run_address>0x127c</run_address>
         <size>0x70</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x12ec</load_address>
         <run_address>0x12ec</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-15">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x950</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.debug_line</name>
         <load_address>0x950</load_address>
         <run_address>0x950</run_address>
         <size>0x3d</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x98d</load_address>
         <run_address>0x98d</run_address>
         <size>0x29e</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.debug_line</name>
         <load_address>0xc2b</load_address>
         <run_address>0xc2b</run_address>
         <size>0x2fa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_line</name>
         <load_address>0xf25</load_address>
         <run_address>0xf25</run_address>
         <size>0x6a</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_line</name>
         <load_address>0xf8f</load_address>
         <run_address>0xf8f</run_address>
         <size>0xe2</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x1071</load_address>
         <run_address>0x1071</run_address>
         <size>0x2aa</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x131b</load_address>
         <run_address>0x131b</run_address>
         <size>0x244</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x155f</load_address>
         <run_address>0x155f</run_address>
         <size>0x1f0</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_line</name>
         <load_address>0x174f</load_address>
         <run_address>0x174f</run_address>
         <size>0x5e6</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_line</name>
         <load_address>0x1d35</load_address>
         <run_address>0x1d35</run_address>
         <size>0x94</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_line</name>
         <load_address>0x1dc9</load_address>
         <run_address>0x1dc9</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_line</name>
         <load_address>0x1e47</load_address>
         <run_address>0x1e47</run_address>
         <size>0xad</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x1ef4</load_address>
         <run_address>0x1ef4</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_line</name>
         <load_address>0x1f44</load_address>
         <run_address>0x1f44</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_line</name>
         <load_address>0x1fa6</load_address>
         <run_address>0x1fa6</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_line</name>
         <load_address>0x1fe4</load_address>
         <run_address>0x1fe4</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0x201e</load_address>
         <run_address>0x201e</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x2083</load_address>
         <run_address>0x2083</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_line</name>
         <load_address>0x20e1</load_address>
         <run_address>0x20e1</run_address>
         <size>0x4f</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-16">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x201</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_abbrev</name>
         <load_address>0x201</load_address>
         <run_address>0x201</run_address>
         <size>0x63</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-24">
         <name>.debug_abbrev</name>
         <load_address>0x264</load_address>
         <run_address>0x264</run_address>
         <size>0x1c9</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2c">
         <name>.debug_abbrev</name>
         <load_address>0x42d</load_address>
         <run_address>0x42d</run_address>
         <size>0x1de</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_abbrev</name>
         <load_address>0x60b</load_address>
         <run_address>0x60b</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_abbrev</name>
         <load_address>0x62c</load_address>
         <run_address>0x62c</run_address>
         <size>0x14f</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_abbrev</name>
         <load_address>0x77b</load_address>
         <run_address>0x77b</run_address>
         <size>0x1cd</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_abbrev</name>
         <load_address>0x948</load_address>
         <run_address>0x948</run_address>
         <size>0x1cf</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_abbrev</name>
         <load_address>0xb17</load_address>
         <run_address>0xb17</run_address>
         <size>0x18d</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_abbrev</name>
         <load_address>0xca4</load_address>
         <run_address>0xca4</run_address>
         <size>0x1f6</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_abbrev</name>
         <load_address>0xe9a</load_address>
         <run_address>0xe9a</run_address>
         <size>0xb1</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_abbrev</name>
         <load_address>0xf4b</load_address>
         <run_address>0xf4b</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0xf91</load_address>
         <run_address>0xf91</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_abbrev</name>
         <load_address>0xfc9</load_address>
         <run_address>0xfc9</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_abbrev</name>
         <load_address>0x10ac</load_address>
         <run_address>0x10ac</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_abbrev</name>
         <load_address>0x1154</load_address>
         <run_address>0x1154</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_abbrev</name>
         <load_address>0x11bc</load_address>
         <run_address>0x11bc</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_abbrev</name>
         <load_address>0x1222</load_address>
         <run_address>0x1222</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_abbrev</name>
         <load_address>0x1352</load_address>
         <run_address>0x1352</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_abbrev</name>
         <load_address>0x1404</load_address>
         <run_address>0x1404</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_abbrev</name>
         <load_address>0x14f2</load_address>
         <run_address>0x14f2</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-17">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f0</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.debug_aranges</name>
         <load_address>0x1f0</load_address>
         <run_address>0x1f0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-25">
         <name>.debug_aranges</name>
         <load_address>0x208</load_address>
         <run_address>0x208</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.debug_aranges</name>
         <load_address>0x270</load_address>
         <run_address>0x270</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_aranges</name>
         <load_address>0x2f8</load_address>
         <run_address>0x2f8</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_aranges</name>
         <load_address>0x320</load_address>
         <run_address>0x320</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-10"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_aranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x98</size>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_aranges</name>
         <load_address>0x3d8</load_address>
         <run_address>0x3d8</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_aranges</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_aranges</name>
         <load_address>0x480</load_address>
         <run_address>0x480</run_address>
         <size>0xd8</size>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_aranges</name>
         <load_address>0x558</load_address>
         <run_address>0x558</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_aranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_aranges</name>
         <load_address>0x598</load_address>
         <run_address>0x598</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_aranges</name>
         <load_address>0x5b8</load_address>
         <run_address>0x5b8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_aranges</name>
         <load_address>0x5d8</load_address>
         <run_address>0x5d8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_aranges</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_aranges</name>
         <load_address>0x618</load_address>
         <run_address>0x618</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_aranges</name>
         <load_address>0x640</load_address>
         <run_address>0x640</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_aranges</name>
         <load_address>0x670</load_address>
         <run_address>0x670</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-11"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <load_address>0x127</load_address>
         <run_address>0x127</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-4f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-ea" display="no" color="cyan">
         <name>.text.1</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x800</size>
         <contents>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-9d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-eb" display="no" color="cyan">
         <name>.text.2</name>
         <load_address>0x8800</load_address>
         <run_address>0x8800</run_address>
         <size>0x800</size>
         <contents>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-ec" display="no" color="cyan">
         <name>.text.3</name>
         <load_address>0x9000</load_address>
         <run_address>0x9000</run_address>
         <size>0x7a8</size>
         <contents>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-2e"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <split_section id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <contents>
            <logical_group_ref idref="lg-ea"/>
            <logical_group_ref idref="lg-eb"/>
            <logical_group_ref idref="lg-ec"/>
         </contents>
      </split_section>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xf6</load_address>
         <run_address>0xf6</run_address>
         <size>0x31</size>
         <contents>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-27"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-a7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x400</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-e9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0xace6</run_address>
         <size>0x8c</size>
         <contents>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-28"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-bc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0xa800</load_address>
         <run_address>0xa800</run_address>
         <size>0x4e5</size>
         <contents>
            <object_component_ref idref="oc-13"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-29"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-34"/>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.cio</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>ramgs0</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>ramgs1</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-dd" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1412a</size>
         <contents>
            <object_component_ref idref="oc-12"/>
            <object_component_ref idref="oc-18"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-26"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-ed"/>
         </contents>
      </logical_group>
      <logical_group id="lg-df" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1402</size>
         <contents>
            <object_component_ref idref="oc-14"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-2a"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-87"/>
         </contents>
      </logical_group>
      <logical_group id="lg-e1" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2130</size>
         <contents>
            <object_component_ref idref="oc-15"/>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-88"/>
         </contents>
      </logical_group>
      <logical_group id="lg-e3" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1501</size>
         <contents>
            <object_component_ref idref="oc-16"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-ee"/>
         </contents>
      </logical_group>
      <logical_group id="lg-e5" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x690</size>
         <contents>
            <object_component_ref idref="oc-17"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-8a"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="no" color="green">
         <name>BEGIN</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x2</length>
         <used_space>0x2</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM0</name>
         <page_id>0x0</page_id>
         <origin>0xf6</origin>
         <length>0x30a</length>
         <used_space>0x35</used_space>
         <unused_space>0x2d5</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xf6</start_address>
               <size>0x31</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x127</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <available_space>
               <start_address>0x12b</start_address>
               <size>0x2d5</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS0</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x800</length>
         <used_space>0x800</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x800</size>
               <logical_group_ref idref="lg-ea"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS1</name>
         <page_id>0x0</page_id>
         <origin>0x8800</origin>
         <length>0x800</length>
         <used_space>0x800</used_space>
         <unused_space>0x0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8800</start_address>
               <size>0x800</size>
               <logical_group_ref idref="lg-eb"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS2</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x800</length>
         <used_space>0x7a8</used_space>
         <unused_space>0x58</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x9000</start_address>
               <size>0x7a8</size>
               <logical_group_ref idref="lg-ec"/>
            </allocated_space>
            <available_space>
               <start_address>0x97a8</start_address>
               <size>0x58</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS3</name>
         <page_id>0x0</page_id>
         <origin>0x9800</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS4</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC0</name>
         <page_id>0x0</page_id>
         <origin>0x80000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC1</name>
         <page_id>0x0</page_id>
         <origin>0x81000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC2</name>
         <page_id>0x0</page_id>
         <origin>0x82000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC3</name>
         <page_id>0x0</page_id>
         <origin>0x83000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC4</name>
         <page_id>0x0</page_id>
         <origin>0x84000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC5</name>
         <page_id>0x0</page_id>
         <origin>0x85000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC6</name>
         <page_id>0x0</page_id>
         <origin>0x86000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC7</name>
         <page_id>0x0</page_id>
         <origin>0x87000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC8</name>
         <page_id>0x0</page_id>
         <origin>0x88000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC9</name>
         <page_id>0x0</page_id>
         <origin>0x89000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC10</name>
         <page_id>0x0</page_id>
         <origin>0x8a000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC11</name>
         <page_id>0x0</page_id>
         <origin>0x8b000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC12</name>
         <page_id>0x0</page_id>
         <origin>0x8c000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC13</name>
         <page_id>0x0</page_id>
         <origin>0x8d000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC14</name>
         <page_id>0x0</page_id>
         <origin>0x8e000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK0_SEC15</name>
         <page_id>0x0</page_id>
         <origin>0x8f000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC0</name>
         <page_id>0x0</page_id>
         <origin>0x90000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC1</name>
         <page_id>0x0</page_id>
         <origin>0x91000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC2</name>
         <page_id>0x0</page_id>
         <origin>0x92000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC3</name>
         <page_id>0x0</page_id>
         <origin>0x93000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC4</name>
         <page_id>0x0</page_id>
         <origin>0x94000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC5</name>
         <page_id>0x0</page_id>
         <origin>0x95000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC6</name>
         <page_id>0x0</page_id>
         <origin>0x96000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC7</name>
         <page_id>0x0</page_id>
         <origin>0x97000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC8</name>
         <page_id>0x0</page_id>
         <origin>0x98000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC9</name>
         <page_id>0x0</page_id>
         <origin>0x99000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC10</name>
         <page_id>0x0</page_id>
         <origin>0x9a000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC11</name>
         <page_id>0x0</page_id>
         <origin>0x9b000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC12</name>
         <page_id>0x0</page_id>
         <origin>0x9c000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC13</name>
         <page_id>0x0</page_id>
         <origin>0x9d000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC14</name>
         <page_id>0x0</page_id>
         <origin>0x9e000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH_BANK1_SEC15</name>
         <page_id>0x0</page_id>
         <origin>0x9f000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BOOT_RSVD</name>
         <page_id>0x1</page_id>
         <origin>0x2</origin>
         <length>0xf1</length>
         <used_space>0x0</used_space>
         <unused_space>0xf1</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMM1</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x3f8</length>
         <used_space>0x100</used_space>
         <unused_space>0x2f8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x400</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-8"/>
            </allocated_space>
            <available_space>
               <start_address>0x500</start_address>
               <size>0x2f8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS5</name>
         <page_id>0x1</page_id>
         <origin>0xa800</origin>
         <length>0x800</length>
         <used_space>0x571</used_space>
         <unused_space>0x28f</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0xa800</start_address>
               <size>0x4e5</size>
               <logical_group_ref idref="lg-b"/>
            </allocated_space>
            <available_space>
               <start_address>0xace5</start_address>
               <size>0x1</size>
            </available_space>
            <allocated_space>
               <start_address>0xace6</start_address>
               <size>0x8c</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0xad72</start_address>
               <size>0x28e</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS6</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMLS7</name>
         <page_id>0x1</page_id>
         <origin>0xb800</origin>
         <length>0x800</length>
         <used_space>0x0</used_space>
         <unused_space>0x800</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS0</name>
         <page_id>0x1</page_id>
         <origin>0xc000</origin>
         <length>0x2000</length>
         <used_space>0x0</used_space>
         <unused_space>0x2000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS1</name>
         <page_id>0x1</page_id>
         <origin>0xe000</origin>
         <length>0x2000</length>
         <used_space>0x0</used_space>
         <unused_space>0x2000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS2</name>
         <page_id>0x1</page_id>
         <origin>0x10000</origin>
         <length>0x2000</length>
         <used_space>0x0</used_space>
         <unused_space>0x2000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>RAMGS3</name>
         <page_id>0x1</page_id>
         <origin>0x12000</origin>
         <length>0x1ff8</length>
         <used_space>0x0</used_space>
         <unused_space>0x1ff8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0xf6</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0xf6</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x100</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x500</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-d">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c6">
         <name>_INTERRUPT_init</name>
         <value>0x871c</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-c8">
         <name>_myGPIOToggle_init</name>
         <value>0x86ef</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-ca">
         <name>_myBoardLED0_GPIO_init</name>
         <value>0x8708</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-cc">
         <name>_ASYSCTL_init</name>
         <value>0x85e3</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-ce">
         <name>_Board_init</name>
         <value>0x855a</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d0">
         <name>_PinMux_init</name>
         <value>0x856d</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d2">
         <name>_GPIO_init</name>
         <value>0x86cf</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d4">
         <name>_myGPIOHigh_init</name>
         <value>0x86d6</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d6">
         <name>_myDACB_init</name>
         <value>0x85ef</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-d8">
         <name>_DAC_init</name>
         <value>0x85ec</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-da">
         <name>_ADC_init</name>
         <value>0x8599</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-dc">
         <name>_SYNC_init</name>
         <value>0x8729</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-de">
         <name>_myADCA_init</name>
         <value>0x859c</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-e0">
         <name>_EPWM_init</name>
         <value>0x860e</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-11f">
         <name>_INT_myADCA_1_ISR</name>
         <value>0x8c02</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-121">
         <name>_cntl_2p2z_coeffs1</name>
         <value>0xad52</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-122">
         <name>_main</name>
         <value>0x8ac0</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-124">
         <name>_Ts</name>
         <value>0xad4c</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-125">
         <name>_DEBUG_TOGGLE</name>
         <value>0xad40</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-126">
         <name>_wo</name>
         <value>0xad48</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-127">
         <name>_wc</name>
         <value>0xad4a</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-128">
         <name>_SINE_ENABLE</name>
         <value>0xad43</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-129">
         <name>_DacOutput</name>
         <value>0xad41</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-12a">
         <name>_AdcBuf</name>
         <value>0xad00</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-12b">
         <name>_Kr</name>
         <value>0xad46</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-12c">
         <name>_Kp</name>
         <value>0xad44</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-12d">
         <name>_DacOffset</name>
         <value>0xad42</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-12e">
         <name>_temp</name>
         <value>0xad4e</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-12f">
         <name>_cntl_2p2z_vars1</name>
         <value>0xad62</value>
         <object_component_ref idref="oc-1f"/>
      </symbol>
      <symbol id="sm-155">
         <name>_Example_Fail</name>
         <value>0xacee</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-156">
         <name>_Device_verifyXTAL</name>
         <value>0x89d6</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-158">
         <name>_Device_initGPIO</name>
         <value>0x89c6</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-15a">
         <name>___error__</name>
         <value>0x8a09</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-15c">
         <name>_Device_init</name>
         <value>0x8895</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-15e">
         <name>_Example_PassCount</name>
         <value>0xacec</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-15f">
         <name>_Device_enableAllPeripherals</name>
         <value>0x88e6</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-170">
         <name>code_start</name>
         <value>0x0</value>
         <object_component_ref idref="oc-11"/>
      </symbol>
      <symbol id="sm-181">
         <name>_ADC_setOffsetTrimAll</name>
         <value>0x93e3</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-1e2">
         <name>_DCC_verifyClockFrequency</name>
         <value>0x8ce1</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-213">
         <name>_GPIO_setAnalogMode</name>
         <value>0x926e</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-215">
         <name>_GPIO_setControllerCore</name>
         <value>0x92a8</value>
         <object_component_ref idref="oc-a4"/>
      </symbol>
      <symbol id="sm-217">
         <name>_GPIO_setPinConfig</name>
         <value>0x92df</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-219">
         <name>_GPIO_setDirectionMode</name>
         <value>0x87cf</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-21b">
         <name>_GPIO_setQualificationMode</name>
         <value>0x9316</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-21d">
         <name>_GPIO_setPadConfig</name>
         <value>0x919a</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-24c">
         <name>_Interrupt_illegalOperationHandler</name>
         <value>0x9780</value>
         <object_component_ref idref="oc-47"/>
      </symbol>
      <symbol id="sm-24e">
         <name>_Interrupt_enable</name>
         <value>0x934d</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-250">
         <name>_Interrupt_initVectorTable</name>
         <value>0x94d3</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-252">
         <name>_Interrupt_initModule</name>
         <value>0x9231</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-254">
         <name>_Interrupt_nmiHandler</name>
         <value>0x978a</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-256">
         <name>_Interrupt_defaultHandler</name>
         <value>0x9607</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>_SysCtl_selectXTALSingleEnded</name>
         <value>0x9477</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-2fb">
         <name>_SysCtl_selectXTAL</name>
         <value>0x9384</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-2fd">
         <name>_SysCtl_delay</name>
         <value>0x127</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-2fe">
         <name>_SysCtl_selectOscSource</name>
         <value>0x8fb0</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-300">
         <name>_SysCtl_isPLLValid</name>
         <value>0x8e6c</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-302">
         <name>_SysCtl_setClock</name>
         <value>0x8dac</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-304">
         <name>_SysCtl_getLowSpeedClock</name>
         <value>0x957e</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-306">
         <name>_SysCtl_getClock</name>
         <value>0x90e6</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-312">
         <name>_CNTL_2P2Z_F_VARS_init</name>
         <value>0x8f29</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-313">
         <name>_CNTL_2P2Z_F_COEFFS_init</name>
         <value>0x8f93</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-314">
         <name>_CNTL_2P2Z_F_FUNC</name>
         <value>0x8f3f</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-32f">
         <name>_c_int00</name>
         <value>0x9144</value>
         <object_component_ref idref="oc-5a"/>
      </symbol>
      <symbol id="sm-330">
         <name>__stack</name>
         <value>0x400</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-341">
         <name>FS$$DIV</name>
         <value>0x8747</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-350">
         <name>_copy_in</name>
         <value>0x9453</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-362">
         <name>_memcpy</name>
         <value>0x94f1</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-36f">
         <name>__system_pre_init</name>
         <value>0x97a5</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-37d">
         <name>__system_post_cinit</name>
         <value>0x97a7</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-392">
         <name>C$$EXIT</name>
         <value>0x93ba</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-393">
         <name>_exit</name>
         <value>0x93bc</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-395">
         <name>___TI_cleanup_ptr</name>
         <value>0xace8</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-396">
         <name>___TI_enable_exit_profile_output</name>
         <value>0xace6</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-397">
         <name>_abort</name>
         <value>0x93ba</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-399">
         <name>___TI_dtors_ptr</name>
         <value>0xacea</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-3ad">
         <name>__unlock</name>
         <value>0xacf2</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-3ae">
         <name>__lock</name>
         <value>0xacf0</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-3af">
         <name>__register_lock</name>
         <value>0x9798</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-3b1">
         <name>__nop</name>
         <value>0x979c</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-3b3">
         <name>__register_unlock</name>
         <value>0x9794</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-3c3">
         <name>__args_main</name>
         <value>0x9729</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
