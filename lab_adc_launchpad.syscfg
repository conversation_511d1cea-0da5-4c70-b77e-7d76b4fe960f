/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --board "/boards/LAUNCHXL_F280049C" --context "system" --product "C2000WARE@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const adc              = scripting.addModule("/driverlib/adc.js", {}, false);
const adc1             = adc.addInstance();
const adc2             = adc.addInstance();
const adc3             = adc.addInstance();
const asysctl          = scripting.addModule("/driverlib/asysctl.js");
const led              = scripting.addModule("/driverlib/board_components/led", {}, false);
const led1             = led.addInstance();
const cmpss            = scripting.addModule("/driverlib/cmpss.js", {}, false);
const cmpss1           = cmpss.addInstance();
const dac              = scripting.addModule("/driverlib/dac.js", {}, false);
const dac1             = dac.addInstance();
const dac2             = dac.addInstance();
const epwm             = scripting.addModule("/driverlib/epwm.js", {}, false);
const epwm1            = epwm.addInstance();
const epwm2            = epwm.addInstance();
const epwm3            = epwm.addInstance();
const epwmxbar         = scripting.addModule("/driverlib/epwmxbar.js", {}, false);
const epwmxbar1        = epwmxbar.addInstance();
const gpio             = scripting.addModule("/driverlib/gpio.js", {}, false);
const gpio1            = gpio.addInstance();
const gpio2            = gpio.addInstance();
const gpio3            = gpio.addInstance();
const gpio4            = gpio.addInstance();
const gpio5            = gpio.addInstance();
const gpio6            = gpio.addInstance();
const inputxbar_input  = scripting.addModule("/driverlib/inputxbar_input.js", {}, false);
const inputxbar_input1 = inputxbar_input.addInstance();
const outputxbar       = scripting.addModule("/driverlib/outputxbar.js", {}, false);
const outputxbar1      = outputxbar.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
adc1.$name                   = "myADCA";
adc1.adcClockPrescaler       = "ADC_CLK_DIV_4_0";
adc1.soc0Trigger             = "ADC_TRIGGER_EPWM1_SOCA";
adc1.interruptPulseMode      = "ADC_PULSE_END_OF_CONV";
adc1.enabledInts             = ["ADC_INT_NUMBER1"];
adc1.enableInterrupt1        = true;
adc1.registerInterrupts      = ["1"];
adc1.soc2Trigger             = "ADC_TRIGGER_EPWM1_SOCA";
adc1.soc1Trigger             = "ADC_TRIGGER_EPWM1_SOCA";
adc1.soc1InputCapacitance    = 20;
adc1.soc2SampleWindow        = 20;
adc1.soc1SampleWindow        = 20;
adc1.soc0SampleWindow        = 20;
adc1.enabledSOCs             = ["ADC_SOC_NUMBER0","ADC_SOC_NUMBER1","ADC_SOC_NUMBER2","ADC_SOC_NUMBER3"];
adc1.soc0Channel             = "ADC_CH_ADCIN4";
adc1.soc1Channel             = "ADC_CH_ADCIN5";
adc1.soc2Channel             = "ADC_CH_ADCIN6";
adc1.soc3Channel             = "ADC_CH_ADCIN8";
adc1.soc3SampleWindow        = 20;
adc1.soc3Trigger             = "ADC_TRIGGER_EPWM1_SOCA";
adc1.adcInt1.enableInterrupt = true;

adc2.adcClockPrescaler    = "ADC_CLK_DIV_4_0";
adc2.soc0Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc2.interruptPulseMode   = "ADC_PULSE_END_OF_CONV";
adc2.registerInterrupts   = ["1"];
adc2.soc2Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc2.soc1Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc2.soc1InputCapacitance = 20;
adc2.soc2SampleWindow     = 20;
adc2.soc1SampleWindow     = 20;
adc2.soc0SampleWindow     = 20;
adc2.enabledSOCs          = ["ADC_SOC_NUMBER0","ADC_SOC_NUMBER1","ADC_SOC_NUMBER2","ADC_SOC_NUMBER3"];
adc2.soc3SampleWindow     = 20;
adc2.soc3Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc2.adcBase              = "ADCB_BASE";
adc2.soc1Channel          = "ADC_CH_ADCIN1";
adc2.soc3Channel          = "ADC_CH_ADCIN3";
adc2.soc2Channel          = "ADC_CH_ADCIN2";
adc2.useInterrupts        = false;
adc2.$name                = "myADCB";

adc3.adcClockPrescaler    = "ADC_CLK_DIV_4_0";
adc3.soc0Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc3.interruptPulseMode   = "ADC_PULSE_END_OF_CONV";
adc3.registerInterrupts   = ["1"];
adc3.soc2Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc3.soc1Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc3.soc1InputCapacitance = 20;
adc3.soc2SampleWindow     = 20;
adc3.soc1SampleWindow     = 20;
adc3.soc0SampleWindow     = 20;
adc3.soc3SampleWindow     = 20;
adc3.soc3Trigger          = "ADC_TRIGGER_EPWM1_SOCA";
adc3.useInterrupts        = false;
adc3.adcBase              = "ADCC_BASE";
adc3.soc0Channel          = "ADC_CH_ADCIN4";
adc3.soc1Channel          = "ADC_CH_ADCIN5";
adc3.soc2Channel          = "ADC_CH_ADCIN8";
adc3.soc3Channel          = "ADC_CH_ADCIN14";
adc3.$name                = "myADCC";
adc3.enabledSOCs          = ["ADC_SOC_NUMBER0","ADC_SOC_NUMBER1","ADC_SOC_NUMBER2","ADC_SOC_NUMBER3","ADC_SOC_NUMBER4","ADC_SOC_NUMBER5"];
adc3.soc4SampleWindow     = 20;
adc3.soc5SampleWindow     = 20;
adc3.soc4Channel          = "ADC_CH_ADCIN2";

asysctl.analogReferenceVoltage = "1P65";

led1.$name     = "myBoardLED0";
led1.$hardware = system.deviceData.board.components.LED5;

const multiplier2         = system.clockTree["PLL_IMULT"];
multiplier2.multiplyValue = 10;

const mux2       = system.clockTree["OSCCLKSRCSEL"];
mux2.inputSelect = "X1_XTAL";

cmpss1.$name      = "myCMPSS0";
cmpss1.dacValHigh = 3000;

dac1.enableOutput     = true;
dac1.$name            = "myDACA";
dac1.referenceVoltage = "DAC_REF_ADC_VREFHI";

dac2.dacBase          = "DACB_BASE";
dac2.enableOutput     = true;
dac2.$name            = "myDACB";
dac2.referenceVoltage = "DAC_REF_ADC_VREFHI";

epwm1.epwmTimebase_hsClockDiv                                    = "EPWM_HSCLOCK_DIVIDER_1";
epwm1.epwmEventTrigger_enableInterrupt                           = true;
epwm1.epwmEventTrigger_EPWM_SOC_A_triggerEnable                  = true;
epwm1.epwmEventTrigger_EPWM_SOC_A_triggerSource                  = "EPWM_SOC_TBCTR_PERIOD";
epwm1.epwmEventTrigger_EPWM_SOC_A_triggerEventPrescalar          = "1";
epwm1.$name                                                      = "myEPWM1";
epwm1.$hardware                                                  = system.deviceData.board.components.BP_SITE_2.subComponents.PWM_LOC1;
epwm1.epwmTimebase_counterMode                                   = "EPWM_COUNTER_MODE_UP_DOWN";
epwm1.epwmTimebase_period                                        = 2500;
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowMode            = true;
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_B_shadowMode            = true;
epwm1.epwmDigitalCompare_EPWM_DC_TYPE_DCAH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm1.epwmDigitalCompare_EPWM_DC_TYPE_DCAL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm1.epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_A1                    = "EPWM_TZ_EVENT_DCXH_HIGH";
epwm1.epwmDigitalCompare_EPWM_DC_TYPE_DCBH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm1.epwmDigitalCompare_EPWM_DC_TYPE_DCBL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm1.epwmTripZone_oneShotSource                                 = ["EPWM_TZ_SIGNAL_DCAEVT1"];
epwm1.epwmTripZone_tzInterruptSource                             = ["EPWM_TZ_INTERRUPT_DCAEVT1"];
epwm1.epwmTripZone_registerInterrupts                            = true;
epwm1.epwmCounterCompare_cmpA                                    = 1250;
epwm1.epwmCounterCompare_cmpB                                    = 1250;
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_LOW";
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_HIGH";
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_HIGH";
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_LOW";
epwm1.aQual.$name                                                = "myGPIOQUAL0";
epwm1.bQual.$name                                                = "myGPIOQUAL1";
epwm1.epwmTZInt.enableInterrupt                                  = true;

epwm2.epwmTimebase_hsClockDiv                                    = "EPWM_HSCLOCK_DIVIDER_1";
epwm2.epwmTimebase_counterMode                                   = "EPWM_COUNTER_MODE_UP_DOWN";
epwm2.epwmTimebase_period                                        = 2500;
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowMode            = true;
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_B_shadowMode            = true;
epwm2.epwmDigitalCompare_EPWM_DC_TYPE_DCAH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm2.epwmDigitalCompare_EPWM_DC_TYPE_DCAL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm2.epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_A1                    = "EPWM_TZ_EVENT_DCXH_HIGH";
epwm2.epwmDigitalCompare_EPWM_DC_TYPE_DCBH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm2.epwmDigitalCompare_EPWM_DC_TYPE_DCBL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm2.epwmTripZone_oneShotSource                                 = ["EPWM_TZ_SIGNAL_DCAEVT1"];
epwm2.epwmTripZone_tzInterruptSource                             = ["EPWM_TZ_INTERRUPT_DCAEVT1"];
epwm2.$hardware                                                  = system.deviceData.board.components.BP_SITE_2.subComponents.PWM_LOC3;
epwm2.$name                                                      = "myEPWM2";
epwm2.epwmCounterCompare_cmpA                                    = 1250;
epwm2.epwmCounterCompare_cmpB                                    = 1250;
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_LOW";
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_HIGH";
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_HIGH";
epwm2.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_LOW";

epwm3.epwmTimebase_hsClockDiv                                    = "EPWM_HSCLOCK_DIVIDER_1";
epwm3.epwmTimebase_counterMode                                   = "EPWM_COUNTER_MODE_UP_DOWN";
epwm3.epwmTimebase_period                                        = 2500;
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowMode            = true;
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_B_shadowMode            = true;
epwm3.epwmDigitalCompare_EPWM_DC_TYPE_DCAH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm3.epwmDigitalCompare_EPWM_DC_TYPE_DCAL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm3.epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_A1                    = "EPWM_TZ_EVENT_DCXH_HIGH";
epwm3.epwmDigitalCompare_EPWM_DC_TYPE_DCBH                       = "EPWM_DC_TRIP_TRIPIN4";
epwm3.epwmDigitalCompare_EPWM_DC_TYPE_DCBL                       = "EPWM_DC_TRIP_TRIPIN4";
epwm3.epwmTripZone_oneShotSource                                 = ["EPWM_TZ_SIGNAL_DCAEVT1"];
epwm3.epwmTripZone_tzInterruptSource                             = ["EPWM_TZ_INTERRUPT_DCAEVT1"];
epwm3.epwmCounterCompare_cmpA                                    = 1250;
epwm3.epwmCounterCompare_cmpB                                    = 1250;
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_LOW";
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_HIGH";
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_HIGH";
epwm3.epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_LOW";
epwm3.$hardware                                                  = system.deviceData.board.components.BP_SITE_1.subComponents.PWM_LOC3;
epwm3.$name                                                      = "myEPWM3";

epwmxbar1.$name         = "myEPWMXBAR0";
epwmxbar1.sourceSignals = ["CMPSS1_CTRIPH"];
epwmxbar1.muxesUsed     = ["XBAR_MUX00"];

gpio1.$name           = "GPIO33I2CA_SCL";
gpio1.direction       = "GPIO_DIR_MODE_OUT";
gpio1.gpioPin.$assign = "boosterpack1.33";

gpio2.$name           = "GPIO32I2CA_SDA";
gpio2.direction       = "GPIO_DIR_MODE_OUT";
gpio2.gpioPin.$assign = "boosterpack2.71";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to SW2 on the LaunchPad F280049C\\. Consider selecting it in \'use hardware\' above\\. @@@.+?@@@", gpio2, "gpioPin");

gpio3.$name           = "AKEY1";
gpio3.gpioPin.$assign = "boosterpack1.14";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to SPIA BP on the LaunchPad F280049C\\. Consider selecting it in \'use hardware\' above\\. @@@.+?@@@", gpio3, "gpioPin");

gpio4.$name           = "AKEY2";
gpio4.gpioPin.$assign = "boosterpack1.15";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to SPIA BP on the LaunchPad F280049C\\. Consider selecting it in \'use hardware\' above\\. @@@.+?@@@", gpio4, "gpioPin");

gpio5.$name           = "AKEY3";
gpio5.gpioPin.$assign = "boosterpack1.7";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to SPIA BP on the LaunchPad F280049C\\. Consider selecting it in \'use hardware\' above\\. @@@.+?@@@", gpio5, "gpioPin");

gpio6.$name           = "AKEY4";
gpio6.gpioPin.$assign = "boosterpack1.19";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to SPIA BP on the LaunchPad F280049C\\. Consider selecting it in \'use hardware\' above\\. @@@.+?@@@", gpio6, "gpioPin");

inputxbar_input1.$name          = "myINPUTXBARINPUT0";
inputxbar_input1.inputxbarGpio  = "GPIO1";
inputxbar_input1.inputxbarInput = "XBAR_INPUT2";

outputxbar1.$name              = "myOUTPUTXBAR0";
outputxbar1.muxesUsed          = ["XBAR_MUX00"];
outputxbar1.outputxbar.$assign = "OUTPUTXBAR1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
led1.gpio.gpioPin.$suggestSolution                    = "boosterpack2.52";
epwm1.epwm.$suggestSolution                           = "EPWM1";
epwm1.epwm.epwm_aPin.$suggestSolution                 = "boosterpack2.80";
epwm1.epwm.epwm_bPin.$suggestSolution                 = "boosterpack2.79";
epwm2.epwm.$suggestSolution                           = "EPWM2";
epwm2.epwm.epwm_aPin.$suggestSolution                 = "boosterpack2.76";
epwm2.epwm.epwm_bPin.$suggestSolution                 = "boosterpack2.75";
epwm3.epwm.$suggestSolution                           = "EPWM3";
epwm3.epwm.epwm_aPin.$suggestSolution                 = "boosterpack1.36";
epwm3.epwm.epwm_bPin.$suggestSolution                 = "boosterpack1.35";
outputxbar1.outputxbar.outputxbarPin.$suggestSolution = "boosterpack1.34";
