******************************************************************************
             TMS320C2000 Linker PC v22.6.0                     
******************************************************************************
>> Linked Tu<PERSON> Jan 14 19:05:58 2025

OUTPUT FILE NAME:   <adc_lp_f28004x.out>
ENTRY POINT SYMBOL: "code_start"  address: ********


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  BEGIN                 ********   ********  ********  ********  RWIX
  RAMM0                 000000f6   0000030a  ********  000002ca  RWIX
  RAMLS0                ********   ********  ********  ********  RWIX
  RAMLS1                ********   ********  000007fd  ********  RWIX
  RAMLS2                ********   ********  ********  ********  RWIX
  RAMLS3                ********   ********  ********  ********  RWIX
  RAMLS4                0000a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC5      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC6      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC7      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC8      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC9      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC10     0008a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC11     0008b000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC12     0008c000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC13     0008d000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC14     0008e000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC15     0008f000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC5      00095000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC6      00096000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC7      00097000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC8      00098000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC9      00099000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC10     0009a000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC11     0009b000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC12     0009c000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC13     0009d000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC14     0009e000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC15     0009f000   ********  ********  ********  RWIX
  RESET                 003fffc0   ********  ********  ********  RWIX

PAGE 1:
  BOOT_RSVD             ********   000000f1  ********  000000f1  RWIX
  RAMM1                 00000400   000003f8  00000100  000002f8  RWIX
  RAMLS5                0000a800   ********  000004a5  0000035b  RWIX
  RAMLS6                0000b000   ********  ********  ********  RWIX
  RAMLS7                0000b800   ********  ********  ********  RWIX
  RAMGS0                0000c000   00002000  ********  00002000  RWIX
  RAMGS1                0000e000   00002000  ********  00002000  RWIX
  RAMGS2                00010000   00002000  ********  00002000  RWIX
  RAMGS3                00012000   00001ff8  ********  00001ff8  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
codestart 
*          0    ********    ********     
                  ********    ********     f28004x_codestartbranch.obj (codestart)

.TI.ramfunc 
*          0    000000f6    00000004     
                  000000f6    00000004     driverlib_eabi.lib : sysctl.obj (.TI.ramfunc)

.cinit     0    000000fc    0000003c     
                  000000fc    00000027     (.cinit..data.load) [load image, compression = lzss]
                  00000123    00000001     --HOLE-- [fill = 0]
                  00000124    00000006     (__TI_handler_table)
                  0000012a    00000004     (.cinit..bss.load) [load image, compression = zero_init]
                  0000012e    ********     --HOLE-- [fill = 0]
                  00000130    00000008     (__TI_cinit_table)

.reset     0    003fffc0    ********     DSECT

.stack     1    00000400    00000100     UNINITIALIZED
                  00000400    00000100     --HOLE--

.bss       1    0000ac47    00000034     UNINITIALIZED
                  0000ac47    00000032     lab_main.obj (.bss:AdcBuf)
                  0000ac79    ********     lab_main.obj (.bss)

.init_array 
*          0    000000f6    ********     UNINITIALIZED

.const     1    0000a800    00000447     
                  0000a800    00000158     board.obj (.const:.string)
                  0000a958    000000cc     lab_main.obj (.const:.string)
                  0000aa24    0000009a     driverlib_eabi.lib : sysctl.obj (.const:.string)
                  0000aabe    00000098                        : gpio.obj (.const:.string)
                  0000ab56    00000097                        : dcc.obj (.const:.string)
                  0000abed    00000001     --HOLE-- [fill = 0]
                  0000abee    00000059     device.obj (.const:.string)

.data      1    0000ac7c    0000002a     UNINITIALIZED
                  0000ac7c    00000019     lab_main.obj (.data:QuadratureTable)
                  0000ac95    00000001     --HOLE--
                  0000ac96    00000006     lab_main.obj (.data)
                  0000ac9c    00000006     rts2800_fpu32_eabi.lib : exit.c.obj (.data)
                  0000aca2    ********                            : _lock.c.obj (.data:_lock)
                  0000aca4    ********                            : _lock.c.obj (.data:_unlock)

.text.1    0    ********    ********     
                  ********    00000745     board.obj (.text)
                  00008745    000000bb     lab_main.obj (.text)

.text.2    0    ********    000007fd     
                  ********    00000210     device.obj (.text)
                  00008a10    000000cb     driverlib_eabi.lib : dcc.obj (.text:DCC_verifyClockFrequency)
                  00008adb    000000c0                        : sysctl.obj (.text:SysCtl_setClock)
                  00008b9b    000000bd                        : sysctl.obj (.text:SysCtl_isPLLValid)
                  00008c58    00000088     rts2800_fpu32_eabi.lib : fs_div28.asm.obj (.text)
                  00008ce0    0000007f     lab_main.obj (.text:retain)
                  00008d5f    00000073     driverlib_eabi.lib : dcc.obj (.text:DCC_setCounterSeeds)
                  00008dd2    00000073                        : sysctl.obj (.text:DCC_setCounterSeeds)
                  00008e45    0000005e                        : sysctl.obj (.text:SysCtl_getClock)
                  00008ea3    00000052                        : gpio.obj (.text:GPIO_setPadConfig)
                  00008ef5    00000049                        : sysctl.obj (.text:SysCtl_selectOscSource)
                  00008f3e    00000045                        : sysctl.obj (.text:SysCtl_pollX1Counter)
                  00008f83    0000003d                        : interrupt.obj (.text:Interrupt_initModule)
                  00008fc0    0000003a                        : gpio.obj (.text:GPIO_setAnalogMode)
                  00008ffa    ********     rts2800_fpu32_eabi.lib : pre_init.c.obj (.text)
                  00008ffc    00000001                            : startup.c.obj (.text)

.text.3    0    ********    ********     
                  ********    00000037     driverlib_eabi.lib : gpio.obj (.text:GPIO_setControllerCore)
                  00009037    00000037                        : gpio.obj (.text:GPIO_setPinConfig)
                  0000906e    00000037                        : gpio.obj (.text:GPIO_setQualificationMode)
                  000090a5    00000037                        : interrupt.obj (.text:Interrupt_enable)
                  000090dc    00000036                        : sysctl.obj (.text:SysCtl_selectXTAL)
                  00009112    00000031                        : gpio.obj (.text:GPIO_setDirectionMode)
                  00009143    0000002e     rts2800_fpu32_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00009171    0000002b                            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000919c    00000029                            : exit.c.obj (.text)
                  000091c5    00000028     driverlib_eabi.lib : adc.obj (.text:ADC_setOffsetTrimAll)
                  000091ed    00000024                        : dcc.obj (.text:DCC_enableSingleShotMode)
                  00009211    00000024                        : sysctl.obj (.text:DCC_enableSingleShotMode)
                  00009235    00000020                        : sysctl.obj (.text:SysCtl_selectXTALSingleEnded)
                  00009255    0000001e                        : dcc.obj (.text:DCC_setCounter1ClkSource)
                  00009273    0000001e                        : sysctl.obj (.text:DCC_setCounter1ClkSource)
                  00009291    0000001e                        : interrupt.obj (.text:Interrupt_initVectorTable)
                  000092af    0000001d     rts2800_fpu32_eabi.lib : memcpy.c.obj (.text)
                  000092cc    0000001c     driverlib_eabi.lib : dcc.obj (.text:DCC_getErrorStatus)
                  000092e8    0000001c                        : dcc.obj (.text:DCC_getSingleShotStatus)
                  00009304    0000001c                        : dcc.obj (.text:DCC_setCounter0ClkSource)
                  00009320    0000001c                        : sysctl.obj (.text:DCC_setCounter0ClkSource)
                  0000933c    0000001a                        : sysctl.obj (.text:SysCtl_getLowSpeedClock)
                  00009356    00000017                        : sysctl.obj (.text:SysCtl_enablePeripheral)
                  0000936d    00000017     rts2800_fpu32_eabi.lib : boot28.asm.obj (.text)
                  00009384    00000016     driverlib_eabi.lib : dcc.obj (.text:DCC_disableDoneSignal)
                  0000939a    00000016                        : sysctl.obj (.text:DCC_disableDoneSignal)
                  000093b0    00000016                        : dcc.obj (.text:DCC_enableDoneSignal)
                  000093c6    00000016                        : sysctl.obj (.text:DCC_enableDoneSignal)
                  000093dc    00000016                        : interrupt.obj (.text:Interrupt_defaultHandler)
                  000093f2    00000015                        : dcc.obj (.text:DCC_clearDoneFlag)
                  00009407    00000015                        : sysctl.obj (.text:DCC_clearDoneFlag)
                  0000941c    00000015                        : dcc.obj (.text:DCC_clearErrorFlag)
                  00009431    00000015                        : sysctl.obj (.text:DCC_clearErrorFlag)
                  00009446    00000015                        : dcc.obj (.text:DCC_disableErrorSignal)
                  0000945b    00000015                        : sysctl.obj (.text:DCC_disableErrorSignal)
                  00009470    00000015                        : dcc.obj (.text:DCC_enableErrorSignal)
                  00009485    00000015                        : sysctl.obj (.text:DCC_enableErrorSignal)
                  0000949a    00000014                        : dcc.obj (.text:DCC_disableModule)
                  000094ae    00000014                        : sysctl.obj (.text:DCC_disableModule)
                  000094c2    00000014                        : dcc.obj (.text:DCC_enableModule)
                  000094d6    00000014                        : sysctl.obj (.text:DCC_enableModule)
                  000094ea    00000014                        : gpio.obj (.text:GPIO_isPinValid)
                  000094fe    00000010                        : dcc.obj (.text:DCC_isBaseValid)
                  0000950e    00000010                        : sysctl.obj (.text:DCC_isBaseValid)
                  0000951e    0000000d                        : interrupt.obj (.text:Interrupt_disableGlobal)
                  0000952b    0000000d                        : interrupt.obj (.text:Interrupt_enableGlobal)
                  00009538    0000000d     rts2800_fpu32_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00009545    0000000c                            : args_main.c.obj (.text)
                  00009551    0000000b     driverlib_eabi.lib : sysctl.obj (.text:SysCtl_isMCDClockFailureDetected)
                  0000955c    0000000a                        : interrupt.obj (.text:Interrupt_illegalOperationHandler)
                  00009566    0000000a                        : interrupt.obj (.text:Interrupt_nmiHandler)
                  00009570    00000009     rts2800_fpu32_eabi.lib : _lock.c.obj (.text)
                  00009579    00000008                            : copy_decompress_none.c.obj (.text:decompress:none)
                  00009581    00000008     f28004x_codestartbranch.obj (.text)
                  00009589    00000007     driverlib_eabi.lib : sysctl.obj (.text:SysCtl_resetMCD)

MODULE SUMMARY

       Module                        code   ro data   rw data
       ------                        ----   -------   -------
    .\
       lab_main.obj                  314    204       83     
    +--+-----------------------------+------+---------+---------+
       Total:                        314    204       83     
                                                             
    .\device\
       device.obj                    528    89        0      
       f28004x_codestartbranch.obj   10     0         0      
    +--+-----------------------------+------+---------+---------+
       Total:                        538    89        0      
                                                             
    .\syscfg\
       board.obj                     1861   344       0      
    +--+-----------------------------+------+---------+---------+
       Total:                        1861   344       0      
                                                             
    C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\driverlib_eabi.lib
       sysctl.obj                    1167   154       0      
       dcc.obj                       652    151       0      
       gpio.obj                      374    152       0      
       interrupt.obj                 214    0         0      
       adc.obj                       40     0         0      
    +--+-----------------------------+------+---------+---------+
       Total:                        2447   457       0      
                                                             
    C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\rts2800_fpu32_eabi.lib
       fs_div28.asm.obj              136    0         0      
       exit.c.obj                    41     0         6      
       copy_decompress_lzss.c.obj    46     0         0      
       autoinit.c.obj                43     0         0      
       memcpy.c.obj                  29     0         0      
       boot28.asm.obj                23     0         0      
       _lock.c.obj                   9      0         4      
       copy_zero_init.c.obj          13     0         0      
       args_main.c.obj               12     0         0      
       copy_decompress_none.c.obj    8      0         0      
       pre_init.c.obj                2      0         0      
       startup.c.obj                 1      0         0      
    +--+-----------------------------+------+---------+---------+
       Total:                        363    0         10     
                                                             
       Stack:                        0      0         256    
       Linker Generated:             0      57        0      
    +--+-----------------------------+------+---------+---------+
       Grand Total:                  5523   1151      349    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000130 records: 2, size/record: 4, table size: 8
	.data: load addr=000000fc, load size=00000027 bytes, run addr=0000ac7c, run size=0000002a bytes, compression=lzss
	.bss: load addr=0000012a, load size=00000004 bytes, run addr=0000ac47, run size=00000034 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000124 records: 3, size/record: 2, table size: 6
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000400      10 (00000400)     __stack

0000ac47     2b1 (0000ac40)     AdcBuf
0000ac79     2b1 (0000ac40)     DacOutput
0000ac7a     2b1 (0000ac40)     DacOffset
0000ac7c     2b1 (0000ac40)     QuadratureTable

0000ac96     2b2 (0000ac80)     DEBUG_TOGGLE
0000ac97     2b2 (0000ac80)     SINE_ENABLE
0000ac9c     2b2 (0000ac80)     __TI_enable_exit_profile_output
0000ac9e     2b2 (0000ac80)     __TI_cleanup_ptr
0000aca0     2b2 (0000ac80)     __TI_dtors_ptr
0000aca2     2b2 (0000ac80)     _lock
0000aca4     2b2 (0000ac80)     _unlock


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                             
----  -------   ----                             
0     00008599  ADC_init                         
0     000091c5  ADC_setOffsetTrimAll             
0     000085e3  ASYSCTL_init                     
1     0000ac47  AdcBuf                           
0     0000855a  Board_init                       
0     0000919c  C$$EXIT                          
0     000085ec  DAC_init                         
0     00008a10  DCC_verifyClockFrequency         
1     0000ac96  DEBUG_TOGGLE                     
1     0000ac7a  DacOffset                        
1     0000ac79  DacOutput                        
0     000088e6  Device_enableAllPeripherals      
0     00008895  Device_init                      
0     000089c6  Device_initGPIO                  
0     000089d6  Device_verifyXTAL                
0     0000860e  EPWM_init                        
0     000086cd  GPIO_init                        
0     00008fc0  GPIO_setAnalogMode               
0     ********  GPIO_setControllerCore           
0     00009112  GPIO_setDirectionMode            
0     00008ea3  GPIO_setPadConfig                
0     00009037  GPIO_setPinConfig                
0     0000906e  GPIO_setQualificationMode        
0     0000871a  INTERRUPT_init                   
0     00008ce0  INT_myADCA_1_ISR                 
0     000093dc  Interrupt_defaultHandler         
0     000090a5  Interrupt_enable                 
0     0000955c  Interrupt_illegalOperationHandler
0     00008f83  Interrupt_initModule             
0     00009291  Interrupt_initVectorTable        
0     00009566  Interrupt_nmiHandler             
0     0000856d  PinMux_init                      
1     0000ac7c  QuadratureTable                  
1     0000ac97  SINE_ENABLE                      
0     00008727  SYNC_init                        
0     000000f6  SysCtl_delay                     
0     00008e45  SysCtl_getClock                  
0     0000933c  SysCtl_getLowSpeedClock          
0     00008b9b  SysCtl_isPLLValid                
0     00008ef5  SysCtl_selectOscSource           
0     000090dc  SysCtl_selectXTAL                
0     00009235  SysCtl_selectXTALSingleEnded     
0     00008adb  SysCtl_setClock                  
0     00000130  __TI_CINIT_Base                  
0     00000138  __TI_CINIT_Limit                 
0     00000138  __TI_CINIT_Warm                  
0     00000124  __TI_Handler_Table_Base          
0     0000012a  __TI_Handler_Table_Limit         
1     00000500  __TI_STACK_END                   
abs   00000100  __TI_STACK_SIZE                  
0     00009171  __TI_auto_init_nobinit_nopinit   
1     0000ac9e  __TI_cleanup_ptr                 
0     00009143  __TI_decompress_lzss             
0     00009579  __TI_decompress_none             
1     0000aca0  __TI_dtors_ptr                   
1     0000ac9c  __TI_enable_exit_profile_output  
abs   ffffffff  __TI_pprof_out_hndl              
abs   ffffffff  __TI_prof_data_size              
abs   ffffffff  __TI_prof_data_start             
0     00009538  __TI_zero_init_nomemset          
0     00008c58  __c28xabi_divf                   
n/a   UNDEFED   __c_args__                       
0     00008a09  __error__                        
1     00000400  __stack                          
0     00009545  _args_main                       
0     0000936d  _c_int00                         
1     0000aca2  _lock                            
0     00009578  _nop                             
0     00009574  _register_lock                   
0     00009570  _register_unlock                 
0     00008ffc  _system_post_cinit               
0     00008ffa  _system_pre_init                 
1     0000aca4  _unlock                          
0     0000919c  abort                            
0     ********  code_start                       
0     0000919e  exit                             
0     000087f5  main                             
0     000092af  memcpy                           
0     0000859c  myADCA_init                      
0     00008706  myBoardLED0_GPIO_init            
0     000085ef  myDACB_init                      
0     000086d4  myGPIOHigh_init                  
0     000086ed  myGPIOToggle_init                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                             
----  -------   ----                             
0     ********  code_start                       
0     000000f6  SysCtl_delay                     
0     00000124  __TI_Handler_Table_Base          
0     0000012a  __TI_Handler_Table_Limit         
0     00000130  __TI_CINIT_Base                  
0     00000138  __TI_CINIT_Limit                 
0     00000138  __TI_CINIT_Warm                  
0     0000855a  Board_init                       
0     0000856d  PinMux_init                      
0     00008599  ADC_init                         
0     0000859c  myADCA_init                      
0     000085e3  ASYSCTL_init                     
0     000085ec  DAC_init                         
0     000085ef  myDACB_init                      
0     0000860e  EPWM_init                        
0     000086cd  GPIO_init                        
0     000086d4  myGPIOHigh_init                  
0     000086ed  myGPIOToggle_init                
0     00008706  myBoardLED0_GPIO_init            
0     0000871a  INTERRUPT_init                   
0     00008727  SYNC_init                        
0     000087f5  main                             
0     00008895  Device_init                      
0     000088e6  Device_enableAllPeripherals      
0     000089c6  Device_initGPIO                  
0     000089d6  Device_verifyXTAL                
0     00008a09  __error__                        
0     00008a10  DCC_verifyClockFrequency         
0     00008adb  SysCtl_setClock                  
0     00008b9b  SysCtl_isPLLValid                
0     00008c58  __c28xabi_divf                   
0     00008ce0  INT_myADCA_1_ISR                 
0     00008e45  SysCtl_getClock                  
0     00008ea3  GPIO_setPadConfig                
0     00008ef5  SysCtl_selectOscSource           
0     00008f83  Interrupt_initModule             
0     00008fc0  GPIO_setAnalogMode               
0     00008ffa  _system_pre_init                 
0     00008ffc  _system_post_cinit               
0     ********  GPIO_setControllerCore           
0     00009037  GPIO_setPinConfig                
0     0000906e  GPIO_setQualificationMode        
0     000090a5  Interrupt_enable                 
0     000090dc  SysCtl_selectXTAL                
0     00009112  GPIO_setDirectionMode            
0     00009143  __TI_decompress_lzss             
0     00009171  __TI_auto_init_nobinit_nopinit   
0     0000919c  C$$EXIT                          
0     0000919c  abort                            
0     0000919e  exit                             
0     000091c5  ADC_setOffsetTrimAll             
0     00009235  SysCtl_selectXTALSingleEnded     
0     00009291  Interrupt_initVectorTable        
0     000092af  memcpy                           
0     0000933c  SysCtl_getLowSpeedClock          
0     0000936d  _c_int00                         
0     000093dc  Interrupt_defaultHandler         
0     00009538  __TI_zero_init_nomemset          
0     00009545  _args_main                       
0     0000955c  Interrupt_illegalOperationHandler
0     00009566  Interrupt_nmiHandler             
0     00009570  _register_unlock                 
0     00009574  _register_lock                   
0     00009578  _nop                             
0     00009579  __TI_decompress_none             
1     00000400  __stack                          
1     00000500  __TI_STACK_END                   
1     0000ac47  AdcBuf                           
1     0000ac79  DacOutput                        
1     0000ac7a  DacOffset                        
1     0000ac7c  QuadratureTable                  
1     0000ac96  DEBUG_TOGGLE                     
1     0000ac97  SINE_ENABLE                      
1     0000ac9c  __TI_enable_exit_profile_output  
1     0000ac9e  __TI_cleanup_ptr                 
1     0000aca0  __TI_dtors_ptr                   
1     0000aca2  _lock                            
1     0000aca4  _unlock                          
abs   00000100  __TI_STACK_SIZE                  
abs   ffffffff  __TI_pprof_out_hndl              
abs   ffffffff  __TI_prof_data_size              
abs   ffffffff  __TI_prof_data_start             
n/a   UNDEFED   __c_args__                       

[83 symbols]
