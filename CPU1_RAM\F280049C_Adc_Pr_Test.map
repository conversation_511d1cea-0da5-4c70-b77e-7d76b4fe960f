******************************************************************************
             TMS320C2000 Linker PC v22.6.0                     
******************************************************************************
>> Linked Wed May 28 06:42:43 2025

OUTPUT FILE NAME:   <F280049C_Adc_Pr_Test.out>
ENTRY POINT SYMBOL: "code_start"  address: ********


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  BEGIN                 ********   ********  ********  ********  RWIX
  RAMM0                 000000f6   0000030a  ********  000002d5  RWIX
  RAMLS0                ********   ********  ********  ********  RWIX
  RAMLS1                ********   ********  ********  ********  RWIX
  RAMLS2                ********   ********  000007a8  ********  RWIX
  RAMLS3                ********   ********  ********  ********  RWIX
  RAMLS4                0000a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC5      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC6      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC7      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC8      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC9      ********   ********  ********  ********  RWIX
  FLASH_BANK0_SEC10     0008a000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC11     0008b000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC12     0008c000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC13     0008d000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC14     0008e000   ********  ********  ********  RWIX
  FLASH_BANK0_SEC15     0008f000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC0      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC1      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC2      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC3      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC4      ********   ********  ********  ********  RWIX
  FLASH_BANK1_SEC5      00095000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC6      00096000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC7      00097000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC8      00098000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC9      00099000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC10     0009a000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC11     0009b000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC12     0009c000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC13     0009d000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC14     0009e000   ********  ********  ********  RWIX
  FLASH_BANK1_SEC15     0009f000   ********  ********  ********  RWIX
  RESET                 003fffc0   ********  ********  ********  RWIX

PAGE 1:
  BOOT_RSVD             ********   000000f1  ********  000000f1  RWIX
  RAMM1                 00000400   000003f8  00000100  000002f8  RWIX
  RAMLS5                0000a800   ********  00000571  0000028f  RWIX
  RAMLS6                0000b000   ********  ********  ********  RWIX
  RAMLS7                0000b800   ********  ********  ********  RWIX
  RAMGS0                0000c000   00002000  ********  00002000  RWIX
  RAMGS1                0000e000   00002000  ********  00002000  RWIX
  RAMGS2                00010000   00002000  ********  00002000  RWIX
  RAMGS3                00012000   00001ff8  ********  00001ff8  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
codestart 
*          0    ********    ********     
                  ********    ********     f28004x_codestartbranch.obj (codestart)

.cinit     0    000000f6    00000031     
                  000000f6    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  00000104    0000000d     lab_main.obj (.cinit)
                  00000111    0000000a     device.obj (.cinit)
                  0000011b    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  00000120    00000005                       : _lock.c.obj (.cinit:__unlock)
                  00000125    ********     --HOLE-- [fill = 0]

.TI.ramfunc 
*          0    00000127    00000004     
                  00000127    00000004     driverlib_coff.lib : sysctl.obj (.TI.ramfunc)

.reset     0    003fffc0    ********     DSECT
                  003fffc0    ********     rts2800_fpu32.lib : boot28.asm.obj (.reset)

.stack     1    00000400    00000100     UNINITIALIZED
                  00000400    00000100     --HOLE--

.pinit     0    000000f6    ********     UNINITIALIZED

.econst    1    0000a800    000004e5     
                  0000a800    00000158     board.obj (.econst:.string)
                  0000a958    000000cc     lab_main.obj (.econst:.string)
                  0000aa24    0000009a     driverlib_coff.lib : sysctl.obj (.econst:.string)
                  0000aabe    00000098                        : gpio.obj (.econst:.string)
                  0000ab56    00000097                        : dcc.obj (.econst:.string)
                  0000abed    00000001     --HOLE-- [fill = 0]
                  0000abee    00000059     device.obj (.econst:.string)
                  0000ac47    00000001     --HOLE-- [fill = 0]
                  0000ac48    00000051     driverlib_coff.lib : interrupt.obj (.econst:.string)
                  0000ac99    00000001     --HOLE-- [fill = 0]
                  0000ac9a    0000004b                        : adc.obj (.econst:.string)

.ebss      1    0000ace6    0000008c     UNINITIALIZED
                  0000ace6    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  0000acec    00000004     device.obj (.ebss)
                  0000acf0    ********     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  0000acf2    ********                       : _lock.c.obj (.ebss:__unlock)
                  0000acf4    0000000c     --HOLE--
                  0000ad00    00000032     lab_main.obj (.ebss:_AdcBuf)
                  0000ad32    0000000e     --HOLE--
                  0000ad40    00000032     lab_main.obj (.ebss)

.text.1    0    ********    ********     
                  ********    00000747     board.obj (.text)
                  00008747    00000088     rts2800_fpu32.lib : fs_div28.asm.obj (.text)
                  000087cf    00000031     driverlib_coff.lib : gpio.obj (.text:_GPIO_setDirectionMode)

.text.2    0    ********    ********     
                  ********    00000210     device.obj (.text)
                  00008a10    000001f2     lab_main.obj (.text)
                  00008c02    000000df     lab_main.obj (.text:retain)
                  00008ce1    000000cb     driverlib_coff.lib : dcc.obj (.text:_DCC_verifyClockFrequency)
                  00008dac    000000c0                        : sysctl.obj (.text:_SysCtl_setClock)
                  00008e6c    000000bd                        : sysctl.obj (.text:_SysCtl_isPLLValid)
                  00008f29    00000087     Solar_Lib_Float.lib : CNTL_2P2Z_F.obj (.text)
                  00008fb0    00000049     driverlib_coff.lib : sysctl.obj (.text:_SysCtl_selectOscSource)
                  00008ff9    00000007                        : sysctl.obj (.text:_SysCtl_resetMCD)

.text.3    0    ********    000007a8     
                  ********    00000073     driverlib_coff.lib : dcc.obj (.text:_DCC_setCounterSeeds)
                  00009073    00000073                        : sysctl.obj (.text:_DCC_setCounterSeeds)
                  000090e6    0000005e                        : sysctl.obj (.text:_SysCtl_getClock)
                  00009144    00000056     rts2800_fpu32.lib : boot28.asm.obj (.text)
                  0000919a    00000052     driverlib_coff.lib : gpio.obj (.text:_GPIO_setPadConfig)
                  000091ec    00000045                        : sysctl.obj (.text:_SysCtl_pollX1Counter)
                  00009231    0000003d                        : interrupt.obj (.text:_Interrupt_initModule)
                  0000926e    0000003a                        : gpio.obj (.text:_GPIO_setAnalogMode)
                  000092a8    00000037                        : gpio.obj (.text:_GPIO_setControllerCore)
                  000092df    00000037                        : gpio.obj (.text:_GPIO_setPinConfig)
                  00009316    00000037                        : gpio.obj (.text:_GPIO_setQualificationMode)
                  0000934d    00000037                        : interrupt.obj (.text:_Interrupt_enable)
                  00009384    00000036                        : sysctl.obj (.text:_SysCtl_selectXTAL)
                  000093ba    00000029     rts2800_fpu32.lib : exit.c.obj (.text)
                  000093e3    00000028     driverlib_coff.lib : adc.obj (.text:_ADC_setOffsetTrimAll)
                  0000940b    00000024                        : dcc.obj (.text:_DCC_enableSingleShotMode)
                  0000942f    00000024                        : sysctl.obj (.text:_DCC_enableSingleShotMode)
                  00009453    00000024     rts2800_fpu32.lib : cpy_tbl.c.obj (.text)
                  00009477    00000020     driverlib_coff.lib : sysctl.obj (.text:_SysCtl_selectXTALSingleEnded)
                  00009497    0000001e                        : dcc.obj (.text:_DCC_setCounter1ClkSource)
                  000094b5    0000001e                        : sysctl.obj (.text:_DCC_setCounter1ClkSource)
                  000094d3    0000001e                        : interrupt.obj (.text:_Interrupt_initVectorTable)
                  000094f1    0000001d     rts2800_fpu32.lib : memcpy.c.obj (.text)
                  0000950e    0000001c     driverlib_coff.lib : dcc.obj (.text:_DCC_getErrorStatus)
                  0000952a    0000001c                        : dcc.obj (.text:_DCC_getSingleShotStatus)
                  00009546    0000001c                        : dcc.obj (.text:_DCC_setCounter0ClkSource)
                  00009562    0000001c                        : sysctl.obj (.text:_DCC_setCounter0ClkSource)
                  0000957e    0000001a                        : sysctl.obj (.text:_SysCtl_getLowSpeedClock)
                  00009598    00000017                        : sysctl.obj (.text:_SysCtl_enablePeripheral)
                  000095af    00000016                        : dcc.obj (.text:_DCC_disableDoneSignal)
                  000095c5    00000016                        : sysctl.obj (.text:_DCC_disableDoneSignal)
                  000095db    00000016                        : dcc.obj (.text:_DCC_enableDoneSignal)
                  000095f1    00000016                        : sysctl.obj (.text:_DCC_enableDoneSignal)
                  00009607    00000016                        : interrupt.obj (.text:_Interrupt_defaultHandler)
                  0000961d    00000015                        : dcc.obj (.text:_DCC_clearDoneFlag)
                  00009632    00000015                        : sysctl.obj (.text:_DCC_clearDoneFlag)
                  00009647    00000015                        : dcc.obj (.text:_DCC_clearErrorFlag)
                  0000965c    00000015                        : sysctl.obj (.text:_DCC_clearErrorFlag)
                  00009671    00000015                        : dcc.obj (.text:_DCC_disableErrorSignal)
                  00009686    00000015                        : sysctl.obj (.text:_DCC_disableErrorSignal)
                  0000969b    00000015                        : dcc.obj (.text:_DCC_enableErrorSignal)
                  000096b0    00000015                        : sysctl.obj (.text:_DCC_enableErrorSignal)
                  000096c5    00000014                        : dcc.obj (.text:_DCC_disableModule)
                  000096d9    00000014                        : sysctl.obj (.text:_DCC_disableModule)
                  000096ed    00000014                        : dcc.obj (.text:_DCC_enableModule)
                  00009701    00000014                        : sysctl.obj (.text:_DCC_enableModule)
                  00009715    00000014                        : gpio.obj (.text:_GPIO_isPinValid)
                  00009729    00000012     rts2800_fpu32.lib : args_main.c.obj (.text)
                  0000973b    00000010     driverlib_coff.lib : dcc.obj (.text:_DCC_isBaseValid)
                  0000974b    00000010                        : sysctl.obj (.text:_DCC_isBaseValid)
                  0000975b    0000000d                        : interrupt.obj (.text:_Interrupt_disableGlobal)
                  00009768    0000000d                        : interrupt.obj (.text:_Interrupt_enableGlobal)
                  00009775    0000000b                        : sysctl.obj (.text:_SysCtl_isMCDClockFailureDetected)
                  00009780    0000000a                        : interrupt.obj (.text:_Interrupt_illegalOperationHandler)
                  0000978a    0000000a                        : interrupt.obj (.text:_Interrupt_nmiHandler)
                  00009794    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  0000979d    00000008     f28004x_codestartbranch.obj (.text)
                  000097a5    ********     rts2800_fpu32.lib : pre_init.c.obj (.text)
                  000097a7    00000001                       : startup.c.obj (.text)

MODULE SUMMARY

       Module                        code   initialized data   uninitialized data
       ------                        ----   ----------------   ------------------
    .\
       lab_main.obj                  721    217                100               
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        721    217                100               
                                                                                 
    .\device\
       device.obj                    528    99                 4                 
       f28004x_codestartbranch.obj   10     0                  0                 
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        538    99                 4                 
                                                                                 
    .\syscfg\
       board.obj                     1863   344                0                 
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        1863   344                0                 
                                                                                 
    C:/ti/controlSUITE/libs/app_libs/solar/v1.2/float/lib/Solar_Lib_Float.lib
       CNTL_2P2Z_F.obj               135    0                  0                 
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        135    0                  0                 
                                                                                 
    C:\ti\c2000\C2000Ware_5_01_00_00\driverlib\f28004x\driverlib\ccs\Debug\driverlib_coff.lib
       sysctl.obj                    1167   154                0                 
       dcc.obj                       652    151                0                 
       gpio.obj                      374    152                0                 
       interrupt.obj                 214    81                 0                 
       adc.obj                       40     75                 0                 
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        2447   613                0                 
                                                                                 
    C:\ti\ti-cgt-c2000_22.6.0.LTS\lib\rts2800_fpu32.lib
       fs_div28.asm.obj              136    0                  0                 
       boot28.asm.obj                86     0                  0                 
       exit.c.obj                    41     14                 6                 
       cpy_tbl.c.obj                 36     0                  0                 
       memcpy.c.obj                  29     0                  0                 
       _lock.c.obj                   9      10                 4                 
       args_main.c.obj               18     0                  0                 
       pre_init.c.obj                2      0                  0                 
       startup.c.obj                 1      0                  0                 
    +--+-----------------------------+------+------------------+--------------------+
       Total:                        358    24                 10                
                                                                                 
       Stack:                        0      0                  256               
    +--+-----------------------------+------+------------------+--------------------+
       Grand Total:                  6062   1297               370               


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000400      10 (00000400)     __stack

0000ace6     2b3 (0000acc0)     ___TI_enable_exit_profile_output
0000ace8     2b3 (0000acc0)     ___TI_cleanup_ptr
0000acea     2b3 (0000acc0)     ___TI_dtors_ptr
0000acec     2b3 (0000acc0)     _Example_PassCount
0000acee     2b3 (0000acc0)     _Example_Fail
0000acf0     2b3 (0000acc0)     __lock
0000acf2     2b3 (0000acc0)     __unlock

0000ad00     2b4 (0000ad00)     _AdcBuf

0000ad40     2b5 (0000ad40)     _DEBUG_TOGGLE
0000ad41     2b5 (0000ad40)     _DacOutput
0000ad42     2b5 (0000ad40)     _DacOffset
0000ad43     2b5 (0000ad40)     _SINE_ENABLE
0000ad44     2b5 (0000ad40)     _Kp
0000ad46     2b5 (0000ad40)     _Kr
0000ad48     2b5 (0000ad40)     _wo
0000ad4a     2b5 (0000ad40)     _wc
0000ad4c     2b5 (0000ad40)     _Ts
0000ad4e     2b5 (0000ad40)     _temp
0000ad52     2b5 (0000ad40)     _cntl_2p2z_coeffs1
0000ad62     2b5 (0000ad40)     _cntl_2p2z_vars1


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                              
----  -------   ----                              
abs   ffffffff  .text                             
0     000093ba  C$$EXIT                           
0     00008747  FS$$DIV                           
0     00008599  _ADC_init                         
0     000093e3  _ADC_setOffsetTrimAll             
0     000085e3  _ASYSCTL_init                     
1     0000ad00  _AdcBuf                           
0     0000855a  _Board_init                       
0     00008f93  _CNTL_2P2Z_F_COEFFS_init          
0     00008f3f  _CNTL_2P2Z_F_FUNC                 
0     00008f29  _CNTL_2P2Z_F_VARS_init            
0     000085ec  _DAC_init                         
0     00008ce1  _DCC_verifyClockFrequency         
1     0000ad40  _DEBUG_TOGGLE                     
1     0000ad42  _DacOffset                        
1     0000ad41  _DacOutput                        
0     000088e6  _Device_enableAllPeripherals      
0     00008895  _Device_init                      
0     000089c6  _Device_initGPIO                  
0     000089d6  _Device_verifyXTAL                
0     0000860e  _EPWM_init                        
1     0000acee  _Example_Fail                     
1     0000acec  _Example_PassCount                
0     000086cf  _GPIO_init                        
0     0000926e  _GPIO_setAnalogMode               
0     000092a8  _GPIO_setControllerCore           
0     000087cf  _GPIO_setDirectionMode            
0     0000919a  _GPIO_setPadConfig                
0     000092df  _GPIO_setPinConfig                
0     00009316  _GPIO_setQualificationMode        
0     0000871c  _INTERRUPT_init                   
0     00008c02  _INT_myADCA_1_ISR                 
0     00009607  _Interrupt_defaultHandler         
0     0000934d  _Interrupt_enable                 
0     00009780  _Interrupt_illegalOperationHandler
0     00009231  _Interrupt_initModule             
0     000094d3  _Interrupt_initVectorTable        
0     0000978a  _Interrupt_nmiHandler             
1     0000ad44  _Kp                               
1     0000ad46  _Kr                               
0     0000856d  _PinMux_init                      
1     0000ad43  _SINE_ENABLE                      
0     00008729  _SYNC_init                        
0     00000127  _SysCtl_delay                     
0     000090e6  _SysCtl_getClock                  
0     0000957e  _SysCtl_getLowSpeedClock          
0     00008e6c  _SysCtl_isPLLValid                
0     00008fb0  _SysCtl_selectOscSource           
0     00009384  _SysCtl_selectXTAL                
0     00009477  _SysCtl_selectXTALSingleEnded     
0     00008dac  _SysCtl_setClock                  
1     0000ad4c  _Ts                               
1     00000500  __STACK_END                       
abs   00000100  __STACK_SIZE                      
1     0000ace8  ___TI_cleanup_ptr                 
1     0000acea  ___TI_dtors_ptr                   
1     0000ace6  ___TI_enable_exit_profile_output  
abs   ffffffff  ___TI_pprof_out_hndl              
abs   ffffffff  ___TI_prof_data_size              
abs   ffffffff  ___TI_prof_data_start             
abs   ffffffff  ___binit__                        
abs   ffffffff  ___c_args__                       
0     000000f6  ___cinit__                        
0     00008a09  ___error__                        
abs   ffffffff  ___etext__                        
abs   ffffffff  ___pinit__                        
abs   ffffffff  ___text__                         
0     00009729  __args_main                       
1     0000acf0  __lock                            
0     0000979c  __nop                             
0     00009798  __register_lock                   
0     00009794  __register_unlock                 
1     00000400  __stack                           
0     000097a7  __system_post_cinit               
0     000097a5  __system_pre_init                 
1     0000acf2  __unlock                          
0     000093ba  _abort                            
0     00009144  _c_int00                          
1     0000ad52  _cntl_2p2z_coeffs1                
1     0000ad62  _cntl_2p2z_vars1                  
0     00009453  _copy_in                          
0     000093bc  _exit                             
0     00008ac0  _main                             
0     000094f1  _memcpy                           
0     0000859c  _myADCA_init                      
0     00008708  _myBoardLED0_GPIO_init            
0     000085ef  _myDACB_init                      
0     000086d6  _myGPIOHigh_init                  
0     000086ef  _myGPIOToggle_init                
1     0000ad4e  _temp                             
1     0000ad4a  _wc                               
1     0000ad48  _wo                               
abs   ffffffff  binit                             
0     000000f6  cinit                             
0     ********  code_start                        
abs   ffffffff  etext                             
abs   ffffffff  pinit                             


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                              
----  -------   ----                              
0     ********  code_start                        
0     000000f6  ___cinit__                        
0     000000f6  cinit                             
0     00000127  _SysCtl_delay                     
0     0000855a  _Board_init                       
0     0000856d  _PinMux_init                      
0     00008599  _ADC_init                         
0     0000859c  _myADCA_init                      
0     000085e3  _ASYSCTL_init                     
0     000085ec  _DAC_init                         
0     000085ef  _myDACB_init                      
0     0000860e  _EPWM_init                        
0     000086cf  _GPIO_init                        
0     000086d6  _myGPIOHigh_init                  
0     000086ef  _myGPIOToggle_init                
0     00008708  _myBoardLED0_GPIO_init            
0     0000871c  _INTERRUPT_init                   
0     00008729  _SYNC_init                        
0     00008747  FS$$DIV                           
0     000087cf  _GPIO_setDirectionMode            
0     00008895  _Device_init                      
0     000088e6  _Device_enableAllPeripherals      
0     000089c6  _Device_initGPIO                  
0     000089d6  _Device_verifyXTAL                
0     00008a09  ___error__                        
0     00008ac0  _main                             
0     00008c02  _INT_myADCA_1_ISR                 
0     00008ce1  _DCC_verifyClockFrequency         
0     00008dac  _SysCtl_setClock                  
0     00008e6c  _SysCtl_isPLLValid                
0     00008f29  _CNTL_2P2Z_F_VARS_init            
0     00008f3f  _CNTL_2P2Z_F_FUNC                 
0     00008f93  _CNTL_2P2Z_F_COEFFS_init          
0     00008fb0  _SysCtl_selectOscSource           
0     000090e6  _SysCtl_getClock                  
0     00009144  _c_int00                          
0     0000919a  _GPIO_setPadConfig                
0     00009231  _Interrupt_initModule             
0     0000926e  _GPIO_setAnalogMode               
0     000092a8  _GPIO_setControllerCore           
0     000092df  _GPIO_setPinConfig                
0     00009316  _GPIO_setQualificationMode        
0     0000934d  _Interrupt_enable                 
0     00009384  _SysCtl_selectXTAL                
0     000093ba  C$$EXIT                           
0     000093ba  _abort                            
0     000093bc  _exit                             
0     000093e3  _ADC_setOffsetTrimAll             
0     00009453  _copy_in                          
0     00009477  _SysCtl_selectXTALSingleEnded     
0     000094d3  _Interrupt_initVectorTable        
0     000094f1  _memcpy                           
0     0000957e  _SysCtl_getLowSpeedClock          
0     00009607  _Interrupt_defaultHandler         
0     00009729  __args_main                       
0     00009780  _Interrupt_illegalOperationHandler
0     0000978a  _Interrupt_nmiHandler             
0     00009794  __register_unlock                 
0     00009798  __register_lock                   
0     0000979c  __nop                             
0     000097a5  __system_pre_init                 
0     000097a7  __system_post_cinit               
1     00000400  __stack                           
1     00000500  __STACK_END                       
1     0000ace6  ___TI_enable_exit_profile_output  
1     0000ace8  ___TI_cleanup_ptr                 
1     0000acea  ___TI_dtors_ptr                   
1     0000acec  _Example_PassCount                
1     0000acee  _Example_Fail                     
1     0000acf0  __lock                            
1     0000acf2  __unlock                          
1     0000ad00  _AdcBuf                           
1     0000ad40  _DEBUG_TOGGLE                     
1     0000ad41  _DacOutput                        
1     0000ad42  _DacOffset                        
1     0000ad43  _SINE_ENABLE                      
1     0000ad44  _Kp                               
1     0000ad46  _Kr                               
1     0000ad48  _wo                               
1     0000ad4a  _wc                               
1     0000ad4c  _Ts                               
1     0000ad4e  _temp                             
1     0000ad52  _cntl_2p2z_coeffs1                
1     0000ad62  _cntl_2p2z_vars1                  
abs   00000100  __STACK_SIZE                      
abs   ffffffff  .text                             
abs   ffffffff  ___TI_pprof_out_hndl              
abs   ffffffff  ___TI_prof_data_size              
abs   ffffffff  ___TI_prof_data_start             
abs   ffffffff  ___binit__                        
abs   ffffffff  ___c_args__                       
abs   ffffffff  ___etext__                        
abs   ffffffff  ___pinit__                        
abs   ffffffff  ___text__                         
abs   ffffffff  binit                             
abs   ffffffff  etext                             
abs   ffffffff  pinit                             

[97 symbols]
