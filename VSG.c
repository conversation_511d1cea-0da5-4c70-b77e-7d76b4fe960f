#include "board.h"
#include "VSG.h"
#include "f28004x_device.h"      // Header File Include File
//*********** Function Definition ********//
void VSG_F_init(VSG *v)
{
	v->J = 0;
	v->D = 0;
	v->Pref = 0;
	v->PAC = 0;
	v->Qref= 0;
	v->QAC= 0;
	v->Uref = 0;
	v->theta= (0);
	v->W0 = 0;
	v->E0 = 0;
	v->W1 = 0;
	v->W2 = 0;
	v->W3 = 0;
	v->W4 = 0;
}
void VSG_F_FUNC(VSG *v) {
	//
	v->W1 = __divf32((__divf32(v->Pref, v->W0) - __divf32(v->PAC, v->W0) - v->W1),
			v->J) + v->W2;
	v->W2 = v->W1 + v->W2;
	v->W3 = v->W1 + 314.15926;
	v->theta = v->W3 + v->theta;
	if (v->theta >= 6.28318)
		v->theta = 0;
	//
	v->E0 = __divf32(v->Qref - v->QAC, 40) + v->Uref;
	//
	v->out = __sinpuf32(v->theta) * v->E0;
}
void Virtual_Impedance_F_init(Virtual_Impedance *v) {
	v->T = 0;
	v->K = 0;
	v->Ts = 0;
	v->y = 0.0;
}
void Virtual_Impedance_F_FUNC(Virtual_Impedance *v) {
	v->alpha = __divf32(v->Ts, v->T);
	v->y = v->y + v->alpha * (v->K *v-> u - v->y);
	if(v->y>=v->max)v->y=v->max;
	if(v->y<=v->min)v->y=v->min;
}
