################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
ASM_SRCS += \
../common/source/f28004x_codestartbranch.asm \
../common/source/f28004x_dbgier.asm \
../common/source/f28004x_dcsm_z1otp.asm \
../common/source/f28004x_dcsm_z2otp.asm \
../common/source/f28004x_usdelay.asm 

C_SRCS += \
../common/source/device.c \
../common/source/f28004x_adc.c \
../common/source/f28004x_cputimers.c \
../common/source/f28004x_defaultisr.c \
../common/source/f28004x_dma.c \
../common/source/f28004x_gpio.c \
../common/source/f28004x_piectrl.c \
../common/source/f28004x_pievect.c \
../common/source/f28004x_spi.c \
../common/source/f28004x_sw_prioritized_defaultisr.c \
../common/source/f28004x_sw_prioritized_pievect.c \
../common/source/f28004x_sysctrl.c 

C_DEPS += \
./common/source/device.d \
./common/source/f28004x_adc.d \
./common/source/f28004x_cputimers.d \
./common/source/f28004x_defaultisr.d \
./common/source/f28004x_dma.d \
./common/source/f28004x_gpio.d \
./common/source/f28004x_piectrl.d \
./common/source/f28004x_pievect.d \
./common/source/f28004x_spi.d \
./common/source/f28004x_sw_prioritized_defaultisr.d \
./common/source/f28004x_sw_prioritized_pievect.d \
./common/source/f28004x_sysctrl.d 

OBJS += \
./common/source/device.obj \
./common/source/f28004x_adc.obj \
./common/source/f28004x_codestartbranch.obj \
./common/source/f28004x_cputimers.obj \
./common/source/f28004x_dbgier.obj \
./common/source/f28004x_dcsm_z1otp.obj \
./common/source/f28004x_dcsm_z2otp.obj \
./common/source/f28004x_defaultisr.obj \
./common/source/f28004x_dma.obj \
./common/source/f28004x_gpio.obj \
./common/source/f28004x_piectrl.obj \
./common/source/f28004x_pievect.obj \
./common/source/f28004x_spi.obj \
./common/source/f28004x_sw_prioritized_defaultisr.obj \
./common/source/f28004x_sw_prioritized_pievect.obj \
./common/source/f28004x_sysctrl.obj \
./common/source/f28004x_usdelay.obj 

ASM_DEPS += \
./common/source/f28004x_codestartbranch.d \
./common/source/f28004x_dbgier.d \
./common/source/f28004x_dcsm_z1otp.d \
./common/source/f28004x_dcsm_z2otp.d \
./common/source/f28004x_usdelay.d 

OBJS__QUOTED += \
"common\source\device.obj" \
"common\source\f28004x_adc.obj" \
"common\source\f28004x_codestartbranch.obj" \
"common\source\f28004x_cputimers.obj" \
"common\source\f28004x_dbgier.obj" \
"common\source\f28004x_dcsm_z1otp.obj" \
"common\source\f28004x_dcsm_z2otp.obj" \
"common\source\f28004x_defaultisr.obj" \
"common\source\f28004x_dma.obj" \
"common\source\f28004x_gpio.obj" \
"common\source\f28004x_piectrl.obj" \
"common\source\f28004x_pievect.obj" \
"common\source\f28004x_spi.obj" \
"common\source\f28004x_sw_prioritized_defaultisr.obj" \
"common\source\f28004x_sw_prioritized_pievect.obj" \
"common\source\f28004x_sysctrl.obj" \
"common\source\f28004x_usdelay.obj" 

C_DEPS__QUOTED += \
"common\source\device.d" \
"common\source\f28004x_adc.d" \
"common\source\f28004x_cputimers.d" \
"common\source\f28004x_defaultisr.d" \
"common\source\f28004x_dma.d" \
"common\source\f28004x_gpio.d" \
"common\source\f28004x_piectrl.d" \
"common\source\f28004x_pievect.d" \
"common\source\f28004x_spi.d" \
"common\source\f28004x_sw_prioritized_defaultisr.d" \
"common\source\f28004x_sw_prioritized_pievect.d" \
"common\source\f28004x_sysctrl.d" 

ASM_DEPS__QUOTED += \
"common\source\f28004x_codestartbranch.d" \
"common\source\f28004x_dbgier.d" \
"common\source\f28004x_dcsm_z1otp.d" \
"common\source\f28004x_dcsm_z2otp.d" \
"common\source\f28004x_usdelay.d" 

C_SRCS__QUOTED += \
"../common/source/device.c" \
"../common/source/f28004x_adc.c" \
"../common/source/f28004x_cputimers.c" \
"../common/source/f28004x_defaultisr.c" \
"../common/source/f28004x_dma.c" \
"../common/source/f28004x_gpio.c" \
"../common/source/f28004x_piectrl.c" \
"../common/source/f28004x_pievect.c" \
"../common/source/f28004x_spi.c" \
"../common/source/f28004x_sw_prioritized_defaultisr.c" \
"../common/source/f28004x_sw_prioritized_pievect.c" \
"../common/source/f28004x_sysctrl.c" 

ASM_SRCS__QUOTED += \
"../common/source/f28004x_codestartbranch.asm" \
"../common/source/f28004x_dbgier.asm" \
"../common/source/f28004x_dcsm_z1otp.asm" \
"../common/source/f28004x_dcsm_z2otp.asm" \
"../common/source/f28004x_usdelay.asm" 


